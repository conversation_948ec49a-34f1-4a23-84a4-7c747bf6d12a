#!/usr/bin/env python3
"""
Startup script for the Meeting Intelligence Agent FastAPI server.
Technology Stack: FastAPI + <PERSON><PERSON>hai<PERSON> + Vertex AI
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import app constants
from src.constants.app import (
    APP_NAME,
    APP_DESCRIPTION,
    APP_VERSION,
    LOG_FORMAT,
    LOG_DATE_FORMAT,
    LOG_FILE_PATH,
    LOG_LEVELS,
    DEBUG_MODE
)

def setup_environment():
    """Setup environment variables and directories."""

    # Create necessary directories
    directories = [
        'output',
        'output/json',
        'output/html',
        'logging',
        'feedback'
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f" Created directory: {directory}")

    # Set default environment variables if not already defined
    env_defaults = {
        'OUTPUT_DIR': './output',
        'EMAIL_PROVIDER': 'gmail',
        'ENABLE_DEDUPLICATION': 'true',
        'MAX_RETRIES': '3',
        'DEBUG_MODE': str(DEBUG_MODE).lower()
    }

    for key, default_value in env_defaults.items():
        if not os.getenv(key):
            os.environ[key] = default_value
            print(f" Set {key} = {default_value}")

def check_dependencies():
    """Check if required dependencies are available."""
    print("🔍 Checking dependencies...")

    required_deps = [
        ('fastapi', 'FastAPI web framework'),
        ('uvicorn', 'ASGI server'),
        ('requests', 'HTTP requests library')
    ]

    optional_deps = [
        ('google.auth', 'Google authentication'),
        ('google.cloud.aiplatform', 'Google Cloud Vertex AI'),
        ('sendgrid', 'SendGrid email service'),
        ('langchain', 'LangChain framework'),
        ('langchain_google_vertexai', 'LangChain Vertex AI integration')
    ]

    missing_required = []
    missing_optional = []

    for module, description in required_deps:
        try:
            __import__(module)
            print(f" {description}")
        except ImportError:
            missing_required.append((module, description))
            print(f" {description} - REQUIRED")

    for module, description in optional_deps:
        try:
            __import__(module)
            print(f"✅ {description}")
        except ImportError:
            missing_optional.append((module, description))
            print(f"⚠️ {description} - OPTIONAL")

    if missing_required:
        print("\n Missing required dependencies:")
        for module, desc in missing_required:
            print(f"   pip install {module}")
        return False

    if missing_optional:
        print("\n Missing optional dependencies (some features may not work):")
        for module, desc in missing_optional:
            print(f"   pip install {module}")

    return True

def start_server(host='0.0.0.0', port=8000, debug=False):
    """Start the FastAPI server."""
    try:
        import uvicorn

        # Ensure log directory exists
        os.makedirs(os.path.dirname(LOG_FILE_PATH), exist_ok=True)

        # Configure logging once
        log_level = LOG_LEVELS['DEBUG'] if debug else LOG_LEVELS['INFO']
        logging.basicConfig(
        level=log_level,
        format=LOG_FORMAT,
        datefmt=LOG_DATE_FORMAT,
        handlers=[
            logging.FileHandler(LOG_FILE_PATH),
            logging.StreamHandler(sys.stdout)
        ]
)
        logger = logging.getLogger("startup")
        logger.info(" Logging initialized at level: %s", log_level)


        # Startup information
        print(f"\n Starting {APP_NAME} FastAPI server...")
        print(f"   Version: {APP_VERSION}")
        print(f"   Host: {host}")
        print(f"   Port: {port}")
        print(f"   Environment: {'Development' if debug else 'Production'}")

        print(f"\n📡 API Endpoints:")
        print(f"   GET  /                          - API info")
        print(f"   GET  /health                    - Health check")
        print(f"   GET  /docs                      - Swagger UI")
        print(f"   GET  /agent/summary             - Latest summaries")
        print(f"   POST /agent/trigger             - Manual trigger")
        print(f"   POST /agent/feedback            - Submit feedback")
        print(f"   GET  /agent/feedback/stats      - Feedback stats")
        print(f"   POST /agent/send-email          - Send email")
        print(f"   GET  /agent/status              - Agent status")
        print(f"   GET  /agent/config              - Agent config")

        print(f"\n🔧 Test the API with:")
        print(f"   curl http://{host}:{port}/health")
        print(f"   Open http://{host}:{port}/docs in browser")

        print(f"\n⚙️  Required environment variables:")
        print(f"   GOOGLE_PROJECT_ID, VERTEX_AI_LOCATION")
        print(f"   EMAIL_PROVIDER (gmail/sendgrid), credentials")

        print("\n" + "=" * 60)

        # Launch the FastAPI server
        uvicorn.run(
            "src.api.main:app",
            host=host,
            port=port,
            reload=debug,
            log_level="debug" if debug else "info"
        )

    except ImportError as e:
        print(f" Failed to import FastAPI app: {e}")
        print("➡️ Make sure FastAPI and Uvicorn are installed:")
        print("   pip install fastapi uvicorn[standard]")
        return False
    except Exception as e:
        print(f" Failed to start server: {e}")
        return False

def main():
    """Main entry point."""
    import argparse

    parser = argparse.ArgumentParser(description='Meeting Intelligence Agent FastAPI Server')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=8000, help='Port to bind to (default: 8000)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--check-only', action='store_true', help='Only check dependencies and exit')

    args = parser.parse_args()

    print("\n Meeting Intelligence Agent Startup")
    print(" Technology Stack: FastAPI + LangChain + Vertex AI")
    print("=" * 60)

    setup_environment()

    if not check_dependencies():
        print("\n Dependency check failed. Install required packages.")
        sys.exit(1)

    if args.check_only:
        print("\n Dependency check completed successfully!")
        sys.exit(0)

    try:
        start_server(host=args.host, port=args.port, debug=args.debug)
    except KeyboardInterrupt:
        print("\n Server stopped by user.")
    except Exception as e:
        print(f"\n Server error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
