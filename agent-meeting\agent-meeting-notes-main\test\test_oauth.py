#!/usr/bin/env python3
"""
Test script for unified OAuth2 authentication with Google services.
This script tests Gmail, Calendar, and Drive API access using OAuth2.
"""

import os
import sys
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.services.utility.google_auth import GoogleAuthenticator
from src.services.email_service import EmailService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_oauth_authentication():
    """Test OAuth2 authentication for all Google services."""
    logger.info("Starting OAuth2 authentication test...")
    
    try:
        # Initialize OAuth authenticator
        # Note: This will use the existing gmail-credentials.json as oauth credentials
        # In production, you should rename it to google-oauth-credentials.json
        auth = GoogleAuthenticator(
            credentials_path='./keys/gmail-credentials.json',  # Temporary for testing
            token_path='./keys/google-token.json'
        )
        
        if not auth.credentials:
            logger.error("Failed to initialize OAuth2 credentials")
            return False
        
        # Test authentication for all services
        logger.info("Testing authentication for all Google services...")
        if auth.test_authentication():
            logger.info("✅ All Google services authenticated successfully!")
        else:
            logger.error("❌ Authentication test failed")
            return False
        
        # Test individual services
        logger.info("\n" + "="*50)
        logger.info("Testing individual services...")
        
        # Test Gmail
        logger.info("Testing Gmail service...")
        gmail_service = auth.get_gmail_service()
        if gmail_service:
            try:
                profile = gmail_service.users().getProfile(userId='me').execute()
                logger.info(f"✅ Gmail: Connected as {profile.get('emailAddress', 'Unknown')}")
            except Exception as e:
                logger.error(f"❌ Gmail test failed: {e}")
                return False
        
        # Test Calendar
        logger.info("Testing Calendar service...")
        calendar_service = auth.get_calendar_service()
        if calendar_service:
            try:
                calendars = calendar_service.calendarList().list().execute()
                calendar_count = len(calendars.get('items', []))
                logger.info(f"✅ Calendar: Found {calendar_count} calendars")
            except Exception as e:
                logger.error(f"❌ Calendar test failed: {e}")
                return False
        
        # Test Drive
        logger.info("Testing Drive service...")
        drive_service = auth.get_drive_service()
        if drive_service:
            try:
                about = drive_service.about().get(fields="user,storageQuota").execute()
                user_email = about.get('user', {}).get('emailAddress', 'Unknown')
                logger.info(f"✅ Drive: Connected as {user_email}")
            except Exception as e:
                logger.error(f"❌ Drive test failed: {e}")
                return False
        
        # Test EmailService integration
        logger.info("\nTesting EmailService integration...")
        email_service = EmailService(provider="gmail", google_auth=auth)
        if email_service.gmail_service:
            logger.info("✅ EmailService: Successfully integrated with unified OAuth2")
        else:
            logger.error("❌ EmailService: Failed to integrate with unified OAuth2")
            return False
        
        logger.info("\n" + "="*50)
        logger.info("🎉 All OAuth2 tests passed successfully!")
        logger.info("The unified OAuth2 authentication is working correctly.")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ OAuth2 test failed with error: {e}")
        return False


def show_oauth_setup_instructions():
    """Show instructions for setting up OAuth2 credentials."""
    logger.info("\n" + "="*60)
    logger.info("OAUTH2 SETUP INSTRUCTIONS")
    logger.info("="*60)
    logger.info("""
To set up OAuth2 authentication:

1. Go to Google Cloud Console (https://console.cloud.google.com/)
2. Select your project or create a new one
3. Enable the following APIs:
   - Gmail API
   - Google Calendar API
   - Google Drive API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Choose "Desktop application" as the application type
6. Download the credentials JSON file
7. Rename it to 'google-oauth-credentials.json' and place in ./keys/ folder

Current file structure should be:
./keys/
├── google-oauth-credentials.json  (OAuth2 client credentials)
└── google-token.json             (Generated automatically after first auth)

The system will automatically handle:
- Initial OAuth2 flow (opens browser for consent)
- Token refresh when expired
- Unified access to Gmail, Calendar, and Drive APIs
""")


if __name__ == "__main__":
    logger.info("OAuth2 Authentication Test for Meeting Intelligence Agent")
    logger.info("="*60)
    
    # Check if credentials file exists
    creds_path = Path("./keys/gmail-credentials.json")  # Using existing file for test
    if not creds_path.exists():
        logger.error(f"Credentials file not found: {creds_path}")
        show_oauth_setup_instructions()
        sys.exit(1)
    
    # Run the test
    success = test_oauth_authentication()
    
    if success:
        logger.info("\n✅ OAuth2 implementation is ready for production!")
        logger.info("You can now use the unified GoogleAuthenticator for all Google services.")
    else:
        logger.error("\n❌ OAuth2 test failed. Please check the logs above.")
        show_oauth_setup_instructions()
        sys.exit(1)
