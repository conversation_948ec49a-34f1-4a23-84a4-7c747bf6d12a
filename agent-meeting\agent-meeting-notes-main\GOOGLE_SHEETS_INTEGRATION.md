# Google Sheets Integration for Meeting Intelligence Agent

## Overview

The Google Sheets integration extends the Meeting Intelligence Agent's 6-step workflow to a comprehensive 7-step process that includes automated task management in Google Sheets. This integration provides authenticated user access, automatic spreadsheet creation, and structured task data writing from AI meeting summaries.

## Architecture

### Core Components

1. **GoogleSheetsService** (`src/services/google_sheets_service.py`)
   - Handles all Google Sheets API operations
   - Manages spreadsheet creation, reading, and writing
   - Extracts tasks from AI summarizer output

2. **SheetsToolLangChain** (`src/tools/langchain_sheets_tool.py`)
   - LangChain tool wrapper for Sheets operations
   - Provides structured input/output schemas
   - Integrates with the agent's tool ecosystem

3. **Updated GoogleAuthenticator** (`src/services/utility/google_auth.py`)
   - Extended OAuth scopes to include Sheets API
   - Added `get_sheets_service()` method
   - Enhanced authentication testing

## Workflow Integration

### Updated 7-Step Workflow

1. **Identify Meeting & Transcript** (existing)
2. **Summarize Transcript (AI)** (existing)
3. **Generate JSON & HTML Summaries** (existing)
4. **Email Summaries to Attendees** (existing)
5. **Store Summaries in Google Drive** (existing)
6. **Attach Summary to Calendar Event** (existing)
7. **Extract Tasks to Google Sheets** (NEW)

### Step 7: Task Management

The new step extracts structured task data from the AI summarizer's JSON output and writes it to a Google Sheets spreadsheet with the following columns:

| Column | Description | Example |
|--------|-------------|---------|
| Task Description | The specific task or action item | "Create technical specification document" |
| Priority | Task priority level | HIGH/MEDIUM/LOW |
| Assigned To | Person responsible for the task | "John Smith" |
| Deadline | Task deadline | "2025-02-15" or "Not specified" |
| Meeting Title | Source meeting | "Project Planning Meeting" |
| Date Added | When task was extracted | "2025-01-31 14:30:00" |
| Status | Current task status | "Open" (default) |
| Owner | Meeting outcome owner | "Team Lead" |
| Context | Additional context | "Team agreed on timeline..." |

## Authentication Setup

### Required OAuth Scopes

The integration requires the following Google API scopes:

```python
SCOPES = [
    'https://www.googleapis.com/auth/gmail.send',
    'https://www.googleapis.com/auth/gmail.readonly',
    'https://www.googleapis.com/auth/calendar.readonly',
    'https://www.googleapis.com/auth/calendar.events',
    'https://www.googleapis.com/auth/drive.readonly',
    'https://www.googleapis.com/auth/drive.file',
    'https://www.googleapis.com/auth/spreadsheets'  # NEW
]
```

### Re-authentication Required

Since the Sheets scope is new, users must re-authenticate to grant the additional permission:

1. Delete existing token: `keys/google-token.json`
2. Run re-authentication: `python reauth_with_sheets.py`
3. Complete OAuth flow in browser
4. Verify all services work: `python test_sheets_integration.py`

## Environment Configuration

### New Environment Variables

Add to `.env` file:

```env
# Google Sheets Configuration
GOOGLE_SHEETS_TEMPLATE_ID=
SHEETS_FOLDER_ID=
```

- `GOOGLE_SHEETS_TEMPLATE_ID`: Optional template spreadsheet ID
- `SHEETS_FOLDER_ID`: Optional Google Drive folder for organizing sheets

## API Reference

### GoogleSheetsService Methods

#### Core Operations
- `create_sheet_if_not_exists(user_id, sheet_name)` - Create new task spreadsheet
- `read_range(sheet_id, range_name)` - Read data from sheet range
- `append_rows(sheet_id, rows)` - Append rows to sheet
- `update_cell(sheet_id, cell_range, value)` - Update specific cell

#### Task Management
- `extract_tasks_from_ai_output(ai_output)` - Extract tasks from AI JSON
- `write_tasks_to_sheet(sheet_id, ai_output)` - Write tasks to spreadsheet
- `get_sheet_url(sheet_id)` - Get web URL for spreadsheet

#### Utilities
- `test_connection()` - Test Sheets API connection

### SheetsToolLangChain Actions

#### Available Actions
- `create_sheet` - Create new task spreadsheet for user
- `write_tasks` - Extract and write tasks from AI output
- `read_tasks` - Read existing tasks from spreadsheet
- `update_task` - Update specific task information

#### Input Schema
```python
{
    "action": "create_sheet|write_tasks|read_tasks|update_task",
    "sheet_id": "optional_spreadsheet_id",
    "user_id": "optional_user_identifier", 
    "ai_output": "optional_ai_summarizer_output",
    "task_data": "optional_task_update_data"
}
```

#### Output Schema
```python
{
    "status": "success|error|exists|empty",
    "message": "human_readable_message",
    "sheet_id": "spreadsheet_id",
    "sheet_url": "web_url_to_spreadsheet",
    "tasks_written": "number_of_tasks_written",
    "data": "additional_data"
}
```

## Task Data Structure

### AI Summarizer Output Format

The AI summarizer produces JSON output with this structure:

```json
{
    "meeting_title": "Project Planning Meeting",
    "meeting_date": "2025-01-31",
    "outcomes": [
        {
            "decision": "Implement new feature by Q2",
            "owner": "John Smith",
            "context": "Team agreed on timeline and resource allocation",
            "actions": [
                {
                    "owner": "John Smith",
                    "task": "Create technical specification document",
                    "deadline": "2025-02-15",
                    "priority": "HIGH"
                }
            ]
        }
    ]
}
```

### Extracted Task Rows

Each task becomes a spreadsheet row:

```python
[
    "Create technical specification document",  # Task Description
    "HIGH",                                     # Priority
    "John Smith",                              # Assigned To
    "2025-02-15",                              # Deadline
    "Project Planning Meeting",                # Meeting Title
    "2025-01-31 14:30:00",                    # Date Added
    "Open",                                    # Status
    "John Smith",                              # Owner
    "Implement new feature by Q2 | Team agreed..."  # Context
]
```

## Usage Examples

### Creating a Task Spreadsheet

```python
# Using the service directly
sheets_service = GoogleSheetsService(auth)
sheet_id = sheets_service.create_sheet_if_not_exists("<EMAIL>")

# Using the LangChain tool
sheets_tool = SheetsToolLangChain(auth=auth)
result = sheets_tool._run(
    action="create_sheet",
    user_id="<EMAIL>"
)
```

### Writing Tasks from AI Output

```python
# AI output from summarizer
ai_output = {
    "meeting_title": "Team Standup",
    "outcomes": [...]  # AI-generated outcomes with actions
}

# Write tasks to sheet
success = sheets_service.write_tasks_to_sheet(sheet_id, ai_output)

# Or using LangChain tool
result = sheets_tool._run(
    action="write_tasks",
    sheet_id=sheet_id,
    ai_output=ai_output
)
```

## Testing

### Verification Scripts

1. **verify_sheets_integration.py** - Check code structure and configuration
2. **test_sheets_integration.py** - Test functionality with authentication
3. **reauth_with_sheets.py** - Re-authenticate with Sheets scope

### Test Sequence

```bash
# 1. Verify setup
python verify_sheets_integration.py

# 2. Re-authenticate (if needed)
python reauth_with_sheets.py

# 3. Test integration
python test_sheets_integration.py

# 4. Run full workflow
python run_agent.py
```

## Security Considerations

### OAuth2 Security
- Sheets scope provides read/write access to all user spreadsheets
- Authentication tokens are stored securely in `keys/google-token.json`
- Automatic token refresh prevents expired credential issues

### Data Privacy
- Only meeting-related task data is written to sheets
- User controls which spreadsheets are accessed
- No sensitive meeting content beyond tasks is stored

## Error Handling

### Common Issues
1. **Authentication Errors** - Re-run authentication with Sheets scope
2. **Permission Denied** - Verify OAuth scopes include spreadsheets
3. **Spreadsheet Not Found** - Check sheet ID or create new sheet
4. **API Quota Exceeded** - Implement rate limiting if needed

### Graceful Degradation
- If Sheets integration fails, the 6-step workflow continues normally
- Task extraction errors don't prevent other workflow steps
- Detailed logging helps diagnose issues

## Future Enhancements

### Planned Features
1. **Task Status Updates** - Mark tasks as complete/in-progress
2. **Task Assignment Notifications** - Email assignees about new tasks
3. **Deadline Reminders** - Calendar integration for task deadlines
4. **Task Analytics** - Dashboard for task completion metrics
5. **Template Customization** - User-defined spreadsheet templates

### Integration Opportunities
1. **Project Management Tools** - Sync with Asana, Trello, etc.
2. **Slack Integration** - Post task summaries to team channels
3. **Calendar Blocking** - Automatically block time for high-priority tasks
4. **Reporting** - Weekly/monthly task completion reports

## Troubleshooting

### Common Solutions

**Problem**: "Sheets API authentication failed"
**Solution**: Re-authenticate with `python reauth_with_sheets.py`

**Problem**: "Permission denied" when creating sheets
**Solution**: Verify OAuth consent screen includes Sheets scope

**Problem**: "No tasks found in AI output"
**Solution**: Check AI summarizer output format and task extraction logic

**Problem**: "Spreadsheet not found"
**Solution**: Verify sheet ID or create new sheet with `create_sheet` action

For additional support, check the detailed logs in the console output and refer to the authentication documentation in `AUTHENTICATION_DETAILED_BREAKDOWN.md`.
