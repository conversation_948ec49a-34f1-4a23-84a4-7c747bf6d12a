"""Demo of Google Sheets integration with mock data."""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def demo_task_extraction():
    """Demonstrate task extraction from AI output."""
    print("📋 DEMO: Task Extraction from AI Meeting Summary")
    print("=" * 60)
    
    # Sample AI output (what the summarizer would produce)
    ai_output = {
        "meeting_title": "Project Alpha Q1 Planning Session",
        "meeting_date": "2025-01-31",
        "attendees": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
        "summary": "Project Alpha Q1 planning session focused on technical architecture, timeline planning, and task assignments.",
        "outcomes": [
            {
                "decision": "Finalize technical architecture and database schema",
                "owner": "<PERSON>",
                "context": "Technical foundation needed before API development can begin",
                "actions": [
                    {
                        "owner": "<PERSON>",
                        "task": "Complete database schema design",
                        "deadline": "2025-02-15",
                        "priority": "HIGH"
                    },
                    {
                        "owner": "Mike <PERSON>", 
                        "task": "Prepare integration analysis document",
                        "deadline": "2025-02-08",
                        "priority": "HIGH"
                    },
                    {
                        "owner": "Mike Chen",
                        "task": "Research and recommend performance testing tools",
                        "deadline": "2025-02-12",
                        "priority": "MEDIUM"
                    }
                ]
            },
            {
                "decision": "Implement core API endpoints",
                "owner": "John Smith",
                "context": "APIs needed for frontend integration and testing",
                "actions": [
                    {
                        "owner": "John Smith",
                        "task": "Develop core API endpoints",
                        "deadline": "2025-03-01",
                        "priority": "HIGH"
                    },
                    {
                        "owner": "John Smith",
                        "task": "Set up automated testing framework",
                        "deadline": "2025-02-25",
                        "priority": "MEDIUM"
                    },
                    {
                        "owner": "John Smith",
                        "task": "Create deployment pipeline with CI/CD",
                        "deadline": "2025-03-05",
                        "priority": "MEDIUM"
                    }
                ]
            },
            {
                "decision": "Design user interface and coordinate stakeholder review",
                "owner": "Lisa Rodriguez",
                "context": "UI/UX design needed for development and stakeholder approval",
                "actions": [
                    {
                        "owner": "Lisa Rodriguez",
                        "task": "Prepare stakeholder presentation deck",
                        "deadline": "2025-02-18",
                        "priority": "HIGH"
                    },
                    {
                        "owner": "Lisa Rodriguez",
                        "task": "Coordinate UAT scheduling with business team",
                        "deadline": "2025-02-05",
                        "priority": "MEDIUM"
                    }
                ]
            }
        ]
    }
    
    print(f"📊 Meeting: {ai_output['meeting_title']}")
    print(f"📅 Date: {ai_output['meeting_date']}")
    print(f"👥 Attendees: {', '.join(ai_output['attendees'])}")
    print(f"📋 Outcomes: {len(ai_output['outcomes'])}")
    
    # Simulate task extraction
    try:
        from src.services.google_sheets_service import GoogleSheetsService
        
        # Create service with mock auth (for demonstration)
        class MockAuth:
            def __init__(self):
                self.credentials = None

            def get_sheets_service(self):
                return None

        mock_auth = MockAuth()
        sheets_service = GoogleSheetsService(mock_auth)
        
        # Extract tasks
        tasks = sheets_service.extract_tasks_from_ai_output(ai_output)
        
        print(f"\n✅ Extracted {len(tasks)} tasks:")
        print("\n📊 Task Spreadsheet Preview:")
        print("-" * 120)
        
        # Headers
        headers = [
            "Task Description", "Priority", "Assigned To", "Deadline", 
            "Meeting Title", "Date Added", "Status", "Owner", "Context"
        ]
        
        # Print headers
        header_row = " | ".join(f"{h:<15}" for h in headers)
        print(header_row)
        print("-" * 120)
        
        # Print tasks
        for i, task in enumerate(tasks, 1):
            # Truncate long fields for display
            display_task = [
                task[0][:15] + "..." if len(task[0]) > 15 else task[0],  # Task
                task[1],  # Priority
                task[2][:12] + "..." if len(task[2]) > 12 else task[2],  # Assigned
                task[3],  # Deadline
                task[4][:12] + "..." if len(task[4]) > 12 else task[4],  # Meeting
                task[5][:10],  # Date (just date part)
                task[6],  # Status
                task[7][:12] + "..." if len(task[7]) > 12 else task[7],  # Owner
                task[8][:15] + "..." if len(task[8]) > 15 else task[8]   # Context
            ]
            
            task_row = " | ".join(f"{str(field):<15}" for field in display_task)
            print(f"{i:2d}. {task_row}")
        
        print("-" * 120)
        
        return tasks
        
    except Exception as e:
        print(f"❌ Task extraction error: {e}")
        logger.exception("Task extraction error")
        return []


def demo_langchain_tool():
    """Demonstrate LangChain Sheets tool functionality."""
    print("\n🔧 DEMO: LangChain Sheets Tool")
    print("=" * 60)
    
    try:
        from src.tools.langchain_sheets_tool import SheetsToolLangChain
        
        # Create tool with mock auth
        class MockAuth:
            def __init__(self):
                self.credentials = None

            def get_sheets_service(self):
                return None

        mock_auth = MockAuth()
        sheets_tool = SheetsToolLangChain(auth=mock_auth)
        
        print(f"🛠️ Tool Name: {sheets_tool.name}")
        print(f"📝 Description: {sheets_tool.description}")
        
        print("\n📋 Available Actions:")
        actions = ["create_sheet", "write_tasks", "read_tasks", "update_task"]
        for action in actions:
            print(f"   • {action}")
        
        print("\n📊 Input Schema:")
        print("   {")
        print('     "action": "create_sheet|write_tasks|read_tasks|update_task",')
        print('     "sheet_id": "optional_spreadsheet_id",')
        print('     "user_id": "optional_user_identifier",')
        print('     "ai_output": "optional_ai_summarizer_output",')
        print('     "task_data": "optional_task_update_data"')
        print("   }")
        
        print("\n📤 Output Schema:")
        print("   {")
        print('     "status": "success|error|exists|empty",')
        print('     "message": "human_readable_message",')
        print('     "sheet_id": "spreadsheet_id",')
        print('     "sheet_url": "web_url_to_spreadsheet",')
        print('     "tasks_written": "number_of_tasks_written",')
        print('     "data": "additional_data"')
        print("   }")
        
        return True
        
    except Exception as e:
        print(f"❌ LangChain tool error: {e}")
        logger.exception("LangChain tool error")
        return False


def demo_workflow_integration():
    """Demonstrate workflow integration."""
    print("\n🚀 DEMO: 7-Step Workflow Integration")
    print("=" * 60)
    
    try:
        from src.agents.langchain_meeting_agent import LangChainMeetingAgent
        
        # Initialize agent
        agent = LangChainMeetingAgent({})
        
        print("🤖 Meeting Intelligence Agent - 7-Step Workflow:")
        print()
        
        workflow_steps = [
            "1. 🔍 Identify Meeting & Transcript",
            "2. 🤖 Summarize Transcript (AI)",
            "3. 📄 Generate JSON & HTML Summaries", 
            "4. 📧 Email Summaries to Attendees",
            "5. 💾 Store Summaries in Google Drive",
            "6. 📅 Attach Summary to Calendar Event",
            "7. 📊 Extract Tasks to Google Sheets (NEW)"
        ]
        
        for step in workflow_steps:
            if "NEW" in step:
                print(f"   ✨ {step}")
            else:
                print(f"   ✅ {step}")
        
        # Check tools
        tool_names = [tool.name for tool in agent.tools]
        print(f"\n🛠️ Available Tools ({len(tool_names)}):")
        for tool_name in tool_names:
            if "sheets" in tool_name.lower():
                print(f"   ✨ {tool_name} (NEW)")
            else:
                print(f"   ✅ {tool_name}")
        
        # Check workflow method
        if hasattr(agent, 'execute_7_step_workflow'):
            print(f"\n🔄 Workflow Method: execute_7_step_workflow ✅")
        else:
            print(f"\n🔄 Workflow Method: execute_7_step_workflow ❌")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow integration error: {e}")
        logger.exception("Workflow integration error")
        return False


def demo_authentication_setup():
    """Demonstrate authentication setup."""
    print("\n🔐 DEMO: Authentication Setup")
    print("=" * 60)
    
    try:
        from src.services.utility.google_auth import GoogleAuthenticator
        
        print("📋 Required OAuth2 Scopes:")
        scopes = [
            "https://www.googleapis.com/auth/gmail.send",
            "https://www.googleapis.com/auth/gmail.readonly",
            "https://www.googleapis.com/auth/calendar.readonly",
            "https://www.googleapis.com/auth/calendar.events",
            "https://www.googleapis.com/auth/drive.readonly",
            "https://www.googleapis.com/auth/drive.file",
            "https://www.googleapis.com/auth/spreadsheets"  # NEW
        ]
        
        for scope in scopes:
            if "spreadsheets" in scope:
                print(f"   ✨ {scope} (NEW)")
            else:
                print(f"   ✅ {scope}")
        
        print("\n🔑 Authentication Process:")
        print("   1. Delete existing token (if any)")
        print("   2. Start OAuth2 flow")
        print("   3. User grants permissions in browser")
        print("   4. Token saved for future use")
        print("   5. All services (Gmail, Calendar, Drive, Sheets) ready")
        
        print("\n📝 To Re-authenticate:")
        print("   python reauth_with_sheets.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication demo error: {e}")
        logger.exception("Authentication demo error")
        return False


def main():
    """Run complete demonstration."""
    print("🎬 GOOGLE SHEETS INTEGRATION DEMONSTRATION")
    print("=" * 80)
    print("This demo shows how the Google Sheets integration works")
    print("with sample data from a real meeting transcript.")
    print("=" * 80)
    
    # Demo 1: Task extraction
    tasks = demo_task_extraction()
    if not tasks:
        print("❌ Task extraction demo failed!")
        return
    
    # Demo 2: LangChain tool
    tool_success = demo_langchain_tool()
    if not tool_success:
        print("❌ LangChain tool demo failed!")
        return
    
    # Demo 3: Workflow integration
    workflow_success = demo_workflow_integration()
    if not workflow_success:
        print("❌ Workflow integration demo failed!")
        return
    
    # Demo 4: Authentication setup
    auth_success = demo_authentication_setup()
    if not auth_success:
        print("❌ Authentication demo failed!")
        return
    
    # Success summary
    print("\n" + "=" * 80)
    print("🎉 GOOGLE SHEETS INTEGRATION DEMO COMPLETE!")
    print("=" * 80)
    
    print("✅ Task Extraction: Working")
    print("✅ LangChain Tool: Ready")
    print("✅ Workflow Integration: Complete")
    print("✅ Authentication Setup: Configured")
    
    print(f"\n📊 Demo Results:")
    print(f"   📋 Extracted {len(tasks)} tasks from sample meeting")
    print(f"   🛠️ LangChain tool properly configured")
    print(f"   🚀 7-step workflow ready")
    print(f"   🔐 OAuth2 scopes include Sheets access")
    
    print("\n🎯 What Happens When You Run the Agent:")
    print("   1. Agent detects recent meetings")
    print("   2. AI summarizes meeting transcripts")
    print("   3. Tasks are automatically extracted")
    print("   4. Google Sheets are created with task data")
    print("   5. Team members get organized task lists")
    
    print("\n🚀 Ready to Test with Real Authentication:")
    print("   1. Run: python reauth_with_sheets.py")
    print("   2. Complete OAuth2 flow in browser")
    print("   3. Run: python run_agent.py")
    print("   4. Check your Google Sheets for task management!")


if __name__ == "__main__":
    main()
