"""
Comprehensive verification script for Sheets integration with Lang<PERSON>hain agent.
This script verifies that all components are working together correctly.
"""

import os
import sys
import json
import logging
from datetime import datetime

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_authentication():
    """Test Google OAuth authentication."""
    logger.info("🔐 Testing Google OAuth Authentication...")
    
    try:
        from src.services.utility.google_auth import GoogleAuthenticator
        
        auth = GoogleAuthenticator()
        
        if not auth.credentials or not auth.credentials.valid:
            logger.error("❌ No valid OAuth credentials found")
            logger.info("💡 Please run: python setup_fresh_environment.py")
            return False
        
        user_email = auth.get_user_email()
        if user_email:
            logger.info(f"✅ Authenticated as: {user_email}")
        else:
            logger.error("❌ Could not retrieve user email")
            return False
        
        # Test all services
        services = {
            'Gmail': auth.get_gmail_service(),
            'Calendar': auth.get_calendar_service(),
            'Drive': auth.get_drive_service(),
            'Sheets': auth.get_sheets_service()
        }
        
        for service_name, service in services.items():
            if service:
                logger.info(f"✅ {service_name} service accessible")
            else:
                logger.error(f"❌ {service_name} service not accessible")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Authentication test failed: {e}")
        return False

def test_user_sheet_manager():
    """Test UserSheetManager functionality."""
    logger.info("📊 Testing UserSheetManager...")
    
    try:
        from src.services.utility.google_auth import GoogleAuthenticator
        from src.services.user_sheet_manager import UserSheetManager
        
        auth = GoogleAuthenticator()
        sheet_manager = UserSheetManager(auth)
        
        user_email = auth.get_user_email()
        
        # Test sheet creation/retrieval
        logger.info("🔄 Testing centralized sheet creation...")
        sheet_id = sheet_manager.create_meeting_task_sheet_if_not_exists(user_email)
        
        if sheet_id:
            logger.info(f"✅ Centralized meeting_task sheet ready: {sheet_id}")
        else:
            logger.error("❌ Failed to create/retrieve centralized sheet")
            return False
        
        # Test task extraction
        logger.info("🔄 Testing task extraction...")
        sample_ai_output = {
            "meeting_title": "Integration Test Meeting",
            "action_items": [
                {
                    "task": "Test centralized sheets integration",
                    "owner": "Test User",
                    "deadline": "2024-01-20",
                    "priority": "HIGH",
                    "context": "Verify all components work together"
                }
            ]
        }
        
        tasks = sheet_manager.extract_tasks_from_summary(sample_ai_output)
        if tasks and len(tasks) > 0:
            logger.info(f"✅ Task extraction successful: {len(tasks)} tasks")
        else:
            logger.error("❌ Task extraction failed")
            return False
        
        # Test task writing
        logger.info("🔄 Testing task writing...")
        success = sheet_manager.append_tasks_to_sheet(sheet_id, tasks)
        
        if success:
            logger.info("✅ Task writing successful")
        else:
            logger.error("❌ Task writing failed")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ UserSheetManager test failed: {e}")
        return False

def test_google_sheets_service():
    """Test GoogleSheetsService functionality."""
    logger.info("🔧 Testing GoogleSheetsService...")
    
    try:
        from src.services.utility.google_auth import GoogleAuthenticator
        from src.services.google_sheets_service import GoogleSheetsService
        
        auth = GoogleAuthenticator()
        sheets_service = GoogleSheetsService(auth)
        
        user_email = auth.get_user_email()
        
        # Test centralized sheet ensuring
        logger.info("🔄 Testing centralized sheet ensuring...")
        sheet_id = sheets_service.ensure_user_meeting_task_sheet(user_email)
        
        if sheet_id:
            logger.info(f"✅ Centralized sheet ensured: {sheet_id}")
        else:
            logger.error("❌ Failed to ensure centralized sheet")
            return False
        
        # Test AI output processing
        logger.info("🔄 Testing AI output processing...")
        sample_ai_output = {
            "meeting_title": "Service Test Meeting",
            "action_items": [
                {
                    "task": "Test GoogleSheetsService integration",
                    "owner": "Service Tester",
                    "deadline": "2024-01-21",
                    "priority": "MEDIUM",
                    "context": "Verify service layer works correctly"
                }
            ]
        }
        
        success = sheets_service.write_tasks_from_ai_summary(sample_ai_output, user_email)
        
        if success:
            logger.info("✅ AI output processing successful")
        else:
            logger.error("❌ AI output processing failed")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ GoogleSheetsService test failed: {e}")
        return False

def test_langchain_sheets_tool():
    """Test LangChain Sheets tool integration."""
    logger.info("🤖 Testing LangChain Sheets Tool...")
    
    try:
        from src.tools.langchain_sheets_tool import SheetsToolLangChain
        from src.services.utility.google_auth import GoogleAuthenticator
        
        auth = GoogleAuthenticator()
        sheets_tool = SheetsToolLangChain(auth=auth)
        
        user_email = auth.get_user_email()
        
        # Test create_sheet action
        logger.info("🔄 Testing create_sheet action...")
        create_result = sheets_tool._run(action="create_sheet", user_id=user_email)

        if "ready" in create_result or "success" in create_result:
            logger.info(f"✅ Create sheet action successful: {create_result}")
        else:
            logger.error(f"❌ Create sheet action failed: {create_result}")
            return False
        
        # Test write_tasks action
        logger.info("🔄 Testing write_tasks action...")
        sample_ai_output = {
            "meeting_title": "LangChain Tool Test Meeting",
            "action_items": [
                {
                    "task": "Test LangChain Sheets tool integration",
                    "owner": "LangChain Tester",
                    "deadline": "2024-01-22",
                    "priority": "HIGH",
                    "context": "Verify LangChain tool works with agent"
                }
            ]
        }

        write_result = sheets_tool._run(action="write_tasks", user_id=user_email, ai_output=sample_ai_output)

        if "success" in write_result:
            logger.info(f"✅ Write tasks action successful: {write_result}")
        else:
            logger.error(f"❌ Write tasks action failed: {write_result}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ LangChain Sheets tool test failed: {e}")
        return False

def test_agent_integration():
    """Test that the agent has the Sheets tool properly integrated."""
    logger.info("🎯 Testing Agent Integration...")
    
    try:
        from src.agents.langchain_meeting_agent import LangChainMeetingAgent
        from src.services.utility.google_auth import GoogleAuthenticator
        
        auth = GoogleAuthenticator()
        
        logger.info("🔄 Initializing Meeting Intelligence Agent...")
        agent = LangChainMeetingAgent()
        
        # Check that SheetsToolLangChain is in the tools list
        tool_names = [tool.name for tool in agent.tools]
        
        if "sheets_tool" in tool_names:
            logger.info("✅ Sheets tool found in agent tools")
        else:
            logger.error("❌ Sheets tool not found in agent tools")
            logger.info(f"Available tools: {tool_names}")
            return False
        
        # Check that the agent has the correct system prompt mentioning Step 7
        system_prompt = agent._get_system_prompt()
        if "7. EXTRACT TASKS TO CENTRALIZED GOOGLE SHEETS" in system_prompt:
            logger.info("✅ Agent system prompt includes Step 7 (Sheets)")
        else:
            logger.error("❌ Agent system prompt missing Step 7 (Sheets)")
            return False
        
        logger.info(f"✅ Agent initialized with {len(agent.tools)} tools")
        return True
        
    except Exception as e:
        logger.error(f"❌ Agent integration test failed: {e}")
        return False

def main():
    """Main verification function."""
    logger.info("🔍 Starting Comprehensive Sheets Integration Verification")
    logger.info("=" * 70)
    
    tests = [
        ("Authentication", test_authentication),
        ("UserSheetManager", test_user_sheet_manager),
        ("GoogleSheetsService", test_google_sheets_service),
        ("LangChain Sheets Tool", test_langchain_sheets_tool),
        ("Agent Integration", test_agent_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 TESTING: {test_name}")
        logger.info("-" * 50)
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - PASSED")
            else:
                logger.error(f"❌ {test_name} - FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - ERROR: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 70)
    logger.info("📊 VERIFICATION SUMMARY")
    logger.info("=" * 70)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name:<25} {status}")
    
    logger.info("-" * 70)
    logger.info(f"TOTAL: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED!")
        logger.info("✅ Sheets integration is fully working with LangChain agent")
        logger.info("🚀 Your Meeting Intelligence Agent is ready!")
        return True
    else:
        logger.error("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
