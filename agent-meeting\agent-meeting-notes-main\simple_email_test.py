#!/usr/bin/env python3
"""
Simple Email Test
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# Add src to path
sys.path.append('src')

# Load environment
from dotenv import load_dotenv
load_dotenv()

from src.services.utility.google_auth import GoogleAuthenticator
from src.services.email_service import EmailService
from src.services.notification_service import NotificationService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_direct_email():
    """Test direct email sending."""
    print("🧪 Testing Direct Email Sending")
    print("=" * 40)
    
    try:
        # Initialize services
        auth = GoogleAuthenticator()
        email_service = EmailService(provider="gmail", google_auth=auth)
        
        # Test email
        result = await email_service.send_email(
            to_email="<EMAIL>",
            subject="🧪 Test Email from Meeting Intelligence Agent",
            body="This is a test email to verify email functionality.",
            html_body="""
            <html>
            <body style="font-family: Arial, sans-serif;">
                <h2>🧪 Email Test Successful!</h2>
                <p>This email confirms that the Meeting Intelligence Agent email functionality is working correctly.</p>
                <p><strong>Timestamp:</strong> {}</p>
                <p><strong>Status:</strong> ✅ Email service operational</p>
            </body>
            </html>
            """.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        )
        
        print(f"✅ Email sent successfully!")
        print(f"📊 Result: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Email test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_notification_service():
    """Test notification service."""
    print("\n🧪 Testing Notification Service")
    print("=" * 40)
    
    try:
        # Initialize services
        auth = GoogleAuthenticator()
        email_service = EmailService(provider="gmail", google_auth=auth)
        notification_service = NotificationService(email_service=email_service)
        
        # Test meeting summary
        result = notification_service.send_meeting_summary_email(
            recipients=["<EMAIL>"],
            meeting_title="🧪 Test Meeting Summary",
            meeting_date=datetime.now().strftime('%B %d, %Y'),
            summary_html="""
            <div style="font-family: Arial, sans-serif;">
                <h3>Meeting Summary Test</h3>
                <p>This is a test meeting summary to verify the notification service is working.</p>
                <ul>
                    <li>✅ Email service integration</li>
                    <li>✅ HTML formatting</li>
                    <li>✅ Meeting summary template</li>
                </ul>
            </div>
            """,
            summary_json={
                "meeting_title": "Test Meeting Summary",
                "attendees": ["<EMAIL>"],
                "executive_summary": "Test meeting summary completed successfully.",
                "outcomes": [
                    {
                        "decision": "Email functionality verified",
                        "owner": "Meeting Intelligence Agent",
                        "rationale": "Testing email integration"
                    }
                ]
            }
        )
        
        print(f"✅ Notification service test completed!")
        print(f"📊 Result: {result}")
        return result.get("success", False)
        
    except Exception as e:
        print(f"❌ Notification service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Simple Email Test")
    print("=" * 50)
    
    # Test 1: Direct email
    print("Running direct email test...")
    email_success = asyncio.run(test_direct_email())
    
    # Test 2: Notification service
    print("Running notification service test...")
    notification_success = test_notification_service()
    
    print("\n" + "=" * 50)
    print("📊 FINAL RESULTS")
    print("=" * 50)
    print(f"Direct Email:         {'✅ PASSED' if email_success else '❌ FAILED'}")
    print(f"Notification Service: {'✅ PASSED' if notification_success else '❌ FAILED'}")
    
    if email_success and notification_success:
        print("\n🎉 ALL EMAIL TESTS PASSED!")
        print("📧 Email functionality is now working correctly!")
    else:
        print("\n⚠️  Some tests failed - check the error messages above")
