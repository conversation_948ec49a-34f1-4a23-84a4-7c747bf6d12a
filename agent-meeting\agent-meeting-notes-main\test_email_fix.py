#!/usr/bin/env python3
"""
Test Email Functionality and Fix Issues
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# Add src to path
sys.path.append('src')

from src.services.email_service import EmailService
from src.services.notification_service import NotificationService
from src.services.utility.google_auth import GoogleAuthenticator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_email_service_sync():
    """Test email service with synchronous wrapper."""
    print("🧪 Testing Email Service (Synchronous)")
    print("=" * 50)
    
    try:
        # Initialize Google Auth
        auth = GoogleAuthenticator()
        
        # Initialize Email Service
        email_service = EmailService(provider="gmail", google_auth=auth)
        
        # Test email data
        test_email = "<EMAIL>"  # Use your own email for testing
        subject = "Test Email from Meeting Intelligence Agent"
        body = "This is a test email to verify email functionality is working."
        html_body = """
        <html>
        <body style="font-family: Arial, sans-serif;">
            <h2>🧪 Email Test</h2>
            <p>This is a test email from the Meeting Intelligence Agent.</p>
            <p><strong>Timestamp:</strong> {}</p>
            <p>If you receive this email, the email functionality is working correctly!</p>
        </body>
        </html>
        """.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        print(f"📧 Sending test email to: {test_email}")
        
        # Create async wrapper function
        async def send_test_email():
            return await email_service.send_email(
                to_email=test_email,
                subject=subject,
                body=body,
                html_body=html_body
            )
        
        # Run async function
        result = asyncio.run(send_test_email())
        
        print(f"✅ Email sent successfully!")
        print(f"📊 Result: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Email test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_notification_service():
    """Test notification service."""
    print("\n🧪 Testing Notification Service")
    print("=" * 50)
    
    try:
        # Initialize Google Auth
        auth = GoogleAuthenticator()
        
        # Initialize Email Service
        email_service = EmailService(provider="gmail", google_auth=auth)
        
        # Initialize Notification Service
        notification_service = NotificationService(email_service=email_service)
        
        # Test meeting summary email
        result = notification_service.send_meeting_summary_email(
            recipients=["<EMAIL>"],
            meeting_title="Test Meeting",
            meeting_date=datetime.now().strftime('%B %d, %Y'),
            summary_html="<p>This is a test meeting summary.</p>",
            summary_json={
                "meeting_title": "Test Meeting",
                "attendees": ["<EMAIL>"],
                "executive_summary": "Test meeting completed successfully."
            }
        )
        
        print(f"✅ Notification service test completed!")
        print(f"📊 Result: {result}")
        return result.get("success", False)
        
    except Exception as e:
        print(f"❌ Notification service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_sync_email_service():
    """Create a synchronous email service wrapper."""
    print("\n🔧 Creating Synchronous Email Service Wrapper")
    print("=" * 50)
    
    wrapper_code = '''
import asyncio
from typing import Dict, Any, List, Optional

class SyncEmailService:
    """Synchronous wrapper for EmailService."""
    
    def __init__(self, email_service):
        self.email_service = email_service
    
    def send_email(self, to_email: str, subject: str, body: str,
                   html_body: Optional[str] = None,
                   attachments: Optional[List[str]] = None) -> Dict[str, Any]:
        """Send email synchronously."""
        async def _send():
            return await self.email_service.send_email(
                to_email=to_email,
                subject=subject,
                body=body,
                html_body=html_body,
                attachments=attachments
            )
        
        return asyncio.run(_send())
'''
    
    # Write the wrapper to a file
    with open('src/services/sync_email_service.py', 'w') as f:
        f.write(wrapper_code)
    
    print("✅ Created sync_email_service.py")
    return True

if __name__ == "__main__":
    print("🚀 Email Functionality Test & Fix")
    print("=" * 60)
    
    # Test 1: Direct email service test
    email_success = test_email_service_sync()
    
    # Test 2: Notification service test
    notification_success = test_notification_service()
    
    # Test 3: Create sync wrapper
    wrapper_created = create_sync_email_service()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    print(f"Email Service:        {'✅ PASSED' if email_success else '❌ FAILED'}")
    print(f"Notification Service: {'✅ PASSED' if notification_success else '❌ FAILED'}")
    print(f"Sync Wrapper:         {'✅ CREATED' if wrapper_created else '❌ FAILED'}")
    
    if email_success and notification_success:
        print("\n🎉 All email tests passed!")
    else:
        print("\n⚠️  Some tests failed - need to fix email implementation")
