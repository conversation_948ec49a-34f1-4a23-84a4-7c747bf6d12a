"""Notification service for sending emails and other notifications."""

import asyncio
import logging
from enum import Enum
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime

from .email_service import EmailService

logger = logging.getLogger(__name__)


class NotificationChannel(Enum):
    """Available notification channels."""
    EMAIL = "email"
    


class NotificationPriority(Enum):
    """Notification priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


@dataclass
class NotificationRequest:
    """Request for sending a notification."""
    channel: NotificationChannel
    priority: NotificationPriority
    subject: str
    content: str
    recipients: List[str]
    attachments: Optional[List[Dict[str, Any]]] = None
    metadata: Optional[Dict[str, Any]] = None


class NotificationService:
    """Service for sending notifications through various channels."""

    def __init__(self, email_service: Optional[EmailService] = None):
        """Initialize the notification service."""
        self.email_service = email_service or EmailService()
        
    def send_notification(self, request: NotificationRequest) -> Dict[str, Any]:
        """
        Send a notification through the specified channel.
        
        Args:
            request: The notification request
            
        Returns:
            Result of the notification sending
        """
        try:
            if request.channel == NotificationChannel.EMAIL:
                return self._send_email_notification(request)
            elif request.channel == NotificationChannel.SLACK:
                return self._send_slack_notification(request)
            elif request.channel == NotificationChannel.TEAMS:
                return self._send_teams_notification(request)
            else:
                raise ValueError(f"Unsupported notification channel: {request.channel}")
                
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _send_email_notification(self, request: NotificationRequest) -> Dict[str, Any]:
        """Send email notification."""
        try:
            # Handle multiple recipients by sending individual emails
            # since EmailService.send_email expects a single recipient
            results = []
            successful_recipients = []
            failed_recipients = []

            for recipient in request.recipients:
                try:
                    # Run async email sending synchronously
                    async def send_email_async():
                        return await self.email_service.send_email(
                            to_email=recipient,
                            subject=request.subject,
                            body=request.content,
                            html_body=request.content,
                            attachments=request.attachments or []
                        )

                    result = asyncio.run(send_email_async())
                    results.append(result)
                    successful_recipients.append(recipient)
                    logger.info(f"Successfully sent email to {recipient}")

                except Exception as e:
                    logger.error(f"Failed to send email to {recipient}: {e}")
                    failed_recipients.append(recipient)
                    results.append({"error": str(e), "recipient": recipient})

            return {
                "success": len(successful_recipients) > 0,
                "channel": "email",
                "recipients": request.recipients,
                "successful_recipients": successful_recipients,
                "failed_recipients": failed_recipients,
                "subject": request.subject,
                "results": results,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")
            return {
                "success": False,
                "channel": "email",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _send_slack_notification(self, request: NotificationRequest) -> Dict[str, Any]:
        """Send Slack notification (placeholder)."""
        logger.warning(f"Slack notifications not implemented yet for {request.recipient}")
        return {
            "success": False,
            "channel": "slack",
            "error": "Slack notifications not implemented",
            "timestamp": datetime.now().isoformat()
        }
    
    def _send_teams_notification(self, request: NotificationRequest) -> Dict[str, Any]:
        """Send Teams notification (placeholder)."""
        logger.warning(f"Teams notifications not implemented yet for {request.recipient}")
        return {
            "success": False,
            "channel": "teams",
            "error": "Teams notifications not implemented",
            "timestamp": datetime.now().isoformat()
        }
    
    def send_meeting_summary_email(
        self,
        recipients: List[str],
        meeting_title: str,
        meeting_date: str,
        summary_html: str,
        summary_json: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Send a meeting summary email to attendees.
        
        Args:
            recipients: List of email addresses
            meeting_title: Title of the meeting
            meeting_date: Date of the meeting
            summary_html: HTML content of the summary
            summary_json: Optional JSON summary data
            
        Returns:
            Result of the email sending
        """
        subject = f"Meeting Summary: {meeting_title} - {meeting_date}"
        
        # Create professional email content
        email_content = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .header {{ background-color: #f4f4f4; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .footer {{ background-color: #f4f4f4; padding: 10px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Meeting Summary</h1>
                <h2>{meeting_title}</h2>
                <p><strong>Date:</strong> {meeting_date}</p>
            </div>
            <div class="content">
                {summary_html}
            </div>
            <div class="footer">
                <p>This summary was automatically generated by the Meeting Intelligence Agent.</p>
                <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </body>
        </html>
        """
        
        request = NotificationRequest(
            channel=NotificationChannel.EMAIL,
            priority=NotificationPriority.NORMAL,
            subject=subject,
            content=email_content,
            recipients=recipients,
            metadata={
                "meeting_title": meeting_title,
                "meeting_date": meeting_date,
                "summary_json": summary_json
            }
        )
        
        return self.send_notification(request)
    
    def send_admin_alert(
        self,
        admin_emails: List[str],
        alert_type: str,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Send an admin alert email.
        
        Args:
            admin_emails: List of admin email addresses
            alert_type: Type of alert (error, warning, info)
            message: Alert message
            details: Optional additional details
            
        Returns:
            Result of the email sending
        """
        subject = f"Meeting Intelligence Alert: {alert_type.upper()}"
        
        details_html = ""
        if details:
            details_html = "<h3>Details:</h3><pre>" + str(details) + "</pre>"
        
        email_content = f"""
        <html>
        <body style="font-family: Arial, sans-serif;">
            <h2>Meeting Intelligence System Alert</h2>
            <p><strong>Alert Type:</strong> {alert_type}</p>
            <p><strong>Message:</strong> {message}</p>
            <p><strong>Timestamp:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            {details_html}
        </body>
        </html>
        """
        
        request = NotificationRequest(
            channel=NotificationChannel.EMAIL,
            priority=NotificationPriority.HIGH if alert_type == "error" else NotificationPriority.NORMAL,
            subject=subject,
            content=email_content,
            recipients=admin_emails,
            metadata={
                "alert_type": alert_type,
                "details": details
            }
        )
        
        return self.send_notification(request)
