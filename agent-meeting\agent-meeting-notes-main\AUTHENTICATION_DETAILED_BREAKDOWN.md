# Meeting Intelligence Agent - Authentication Detailed Breakdown

## Overview

This document provides a comprehensive breakdown of how each tool and service in the Meeting Intelligence Agent uses your Google OAuth2 authentication to perform specific operations.

## 🔐 Authentication Foundation

### GoogleAuthenticator Class (`src/services/utility/google_auth.py`)

**Your OAuth2 Scopes:**

```python
SCOPES = [
    'https://www.googleapis.com/auth/gmail.send',           # Send emails
    'https://www.googleapis.com/auth/gmail.readonly',       # Read emails/profile
    'https://www.googleapis.com/auth/calendar.readonly',    # Read calendar events
    'https://www.googleapis.com/auth/calendar.events',      # Modify calendar events
    'https://www.googleapis.com/auth/drive.readonly',       # Read Drive files
    'https://www.googleapis.com/auth/drive.file'            # Create/modify Drive files
]
```

**Authentication Flow:**

1. **Initial Setup**: Uses `google-oauth-credentials.json` (your OAuth2 client credentials)
2. **Token Storage**: Saves access/refresh tokens in `google-token.json`
3. **Auto-Refresh**: Automatically refreshes expired tokens using refresh token
4. **Service Creation**: Creates authenticated service instances for Gmail, Calendar, and Drive

---

## 📅 Calendar Tool - Detailed Authentication Usage

### CalendarTool (`src/tools/langchain_calendar_tool.py`)

**Authentication Integration:**

```python
def __init__(self, auth: GoogleAuthenticator, **kwargs):
    self.auth = auth
    self.calendar_service = GoogleCalendarService(auth)
```

**Specific API Operations with Your Authentication:**

#### 1. **Finding Recent Meetings**

```python
# API Call: calendar.events().list()
events_result = self.service.events().list(
    calendarId='primary',
    timeMin=start_time_str,
    timeMax=end_time_str,
    singleEvents=True,
    orderBy='startTime'
).execute()
```

- **Uses**: `calendar.readonly` scope
- **Accesses**: Your primary calendar and any shared calendars
- **Returns**: Meeting events with attendees, times, descriptions

#### 2. **Getting Event Details**

```python
# API Call: calendar.events().get()
event = self.service.events().get(
    calendarId=calendar_id,
    eventId=event_id
).execute()
```

- **Uses**: `calendar.readonly` scope
- **Accesses**: Detailed event information including:
  - Attendee email addresses
  - Meeting descriptions
  - Location information
  - Organizer details

#### 3. **Adding Attachments to Events**

```python
# API Call: calendar.events().patch()
updated_event = self.service.events().patch(
    calendarId=calendar_id,
    eventId=event_id,
    body={'attachments': attachments}
).execute()
```

- **Uses**: `calendar.events` scope
- **Modifies**: Your calendar events to add meeting summary attachments

---

## 📧 Email Service - Detailed Authentication Usage

### EmailService (`src/services/email_service.py`)

**Authentication Integration:**

```python
def _init_gmail(self):
    self.google_auth = GoogleAuthenticator()
    self.gmail_service = self.google_auth.get_gmail_service()
```

**Specific API Operations with Your Authentication:**

#### 1. **Getting User Profile**

```python
# API Call: gmail.users().getProfile()
profile = gmail_service.users().getProfile(userId='me').execute()
```

- **Uses**: `gmail.readonly` scope
- **Accesses**: Your Gmail profile information
- **Returns**: Your email address for sending notifications

#### 2. **Sending Meeting Summaries**

```python
# API Call: gmail.users().messages().send()
result = self.gmail_service.users().messages().send(
    userId='me',
    body={'raw': raw_message}
).execute()
```

- **Uses**: `gmail.send` scope
- **Sends**: Professional email notifications to meeting attendees
- **Content**: Meeting summaries, action items, and attachments
- **From**: Your authenticated Gmail account

#### 3. **Email Composition Process**

```python
# Creates MIME message with:
message = MIMEMultipart('alternative')
message['to'] = attendee_email
message['from'] = your_gmail_address
message['subject'] = "Meeting Summary: [Meeting Title]"

# Adds HTML and plain text versions
text_part = MIMEText(summary_text, 'plain')
html_part = MIMEText(summary_html, 'html')

# Attaches meeting summary files
attachment = MIMEBase('application', 'octet-stream')
```

---

## 💾 Drive Tool - Detailed Authentication Usage

### DriveTool (`src/tools/langchain_drive_tool.py`)

**Authentication Integration:**

```python
def __init__(self, auth: GoogleAuthenticator, **kwargs):
    self.auth = auth
    self.drive_service = auth.get_drive_service()
```

**Specific API Operations with Your Authentication:**

#### 1. **Searching for Meeting Transcripts**

```python
# API Call: drive.files().list()
results = self.drive_service.files().list(
    q=f"name contains '{meeting_title}' and mimeType contains 'text'",
    fields="files(id, name, mimeType, createdTime, webViewLink)"
).execute()
```

- **Uses**: `drive.readonly` scope
- **Searches**: Your Google Drive for transcript files
- **Filters**: By meeting title, date, and file type

#### 2. **Reading Transcript Content**

```python
# For Google Docs:
file_content = self.drive_service.files().export_media(
    fileId=file_id,
    mimeType='text/plain'
).execute()

# For regular files:
file_content = self.drive_service.files().get_media(
    fileId=file_id
).execute()
```

- **Uses**: `drive.readonly` scope
- **Reads**: Meeting transcript content for AI summarization

#### 3. **Creating Meeting Summary Folders**

```python
# API Call: drive.files().create()
folder_metadata = {
    'name': f"Meeting Summaries - {date}",
    'mimeType': 'application/vnd.google-apps.folder',
    'parents': [parent_folder_id]
}
folder = self.drive_service.files().create(
    body=folder_metadata
).execute()
```

- **Uses**: `drive.file` scope
- **Creates**: Organized folder structure for meeting summaries

#### 4. **Uploading Meeting Summaries**

```python
# API Call: drive.files().create() with media
file_metadata = {
    'name': f"{meeting_title}_Summary.html",
    'parents': [folder_id]
}
media = MediaFileUpload(summary_file_path, mimetype='text/html')
file = self.drive_service.files().create(
    body=file_metadata,
    media_body=media
).execute()
```

- **Uses**: `drive.file` scope
- **Uploads**: Generated meeting summaries (HTML, JSON, PDF formats)
- **Organizes**: Files in structured folders by date and meeting type

---

## 🔗 Calendar Attachment Tool - Combined Authentication Usage

### CalendarAttachmentTool (`src/tools/langchain_calendar_attachment_tool.py`)

**Dual Authentication Integration:**

```python
def __init__(self, auth: GoogleAuthenticator, **kwargs):
    self.auth = auth
    self.calendar_service = GoogleCalendarService(auth)
    self.drive_service = build('drive', 'v3', credentials=auth.credentials)
```

**Combined Operations:**

#### 1. **Upload Summary to Drive + Attach to Calendar**

```python
# Step 1: Upload to Drive (uses drive.file scope)
file_metadata = {'name': summary_filename}
media = MediaFileUpload(summary_path)
drive_file = self.drive_service.files().create(
    body=file_metadata,
    media_body=media
).execute()

# Step 2: Attach to Calendar Event (uses calendar.events scope)
attachment = {
    'fileId': drive_file['id'],
    'title': summary_filename,
    'mimeType': 'text/html'
}
updated_event = self.calendar_service.events().patch(
    calendarId='primary',
    eventId=event_id,
    body={'attachments': [attachment]}
).execute()
```

---

## 🔔 Notification Service - Email Integration

### NotificationService (`src/services/notification_service.py`)

**Uses EmailService with Your Authentication:**

```python
# Sends meeting summaries to attendees
result = self.email_service.send_email(
    to_email=attendee_email,
    subject=f"Meeting Summary: {meeting_title}",
    body=text_summary,
    html_body=html_summary,
    attachments=[summary_file_path]
)
```

---

## 🔄 Complete Workflow Authentication Flow

### How Your Authentication Enables the 6-Step Workflow:

1. **Step 1 - Identify Meetings**: Calendar API reads your calendar events
2. **Step 2 - AI Summarization**: Drive API reads transcript files from your Drive
3. **Step 3 - Generate Summaries**: Drive API uploads generated summaries to your Drive
4. **Step 4 - Email Notifications**: Gmail API sends summaries from your email account
5. **Step 5 - Drive Storage**: Drive API organizes files in your Drive folders
6. **Step 6 - Calendar Attachment**: Calendar API attaches summaries to your calendar events

### Security & Privacy:

- **Your Data**: All operations use YOUR Google account and access YOUR data
- **Permissions**: Limited to specific scopes you authorized during OAuth flow
- **Token Security**: Refresh tokens stored locally, access tokens auto-refreshed
- **API Limits**: Respects Google API rate limits and quotas for your account

---

## 🎯 Summary

Your authentication enables the agent to:

- **Read** your calendar events and meeting details
- **Access** transcript files from your Google Drive
- **Send** professional emails from your Gmail account
- **Create** organized folders and files in your Drive
- **Attach** meeting summaries to your calendar events
- **Operate** completely within your Google Workspace ecosystem

All operations are performed with your authenticated identity, ensuring data privacy and proper access control.

---

## 💻 Code Examples - Exact API Calls Made

### Calendar Tool - Real API Calls

```python
# 1. Finding meetings in the last 30 minutes
def  find_recent_meetings(self):
    events_result = self.service.events().list(
        calendarId='primary',
        timeMin='2025-07-28T16:00:00Z',
        timeMax='2025-07-28T16:30:00Z',
        singleEvents=True,
        orderBy='startTime'
    ).execute()

    # Returns: List of your calendar events with attendees
    return events_result.get('items', [])

# 2. Getting detailed event information
def get_event_details(self, event_id):
    event = self.service.events().get(
        calendarId='primary',
        eventId=event_id
    ).execute()

    # Extracts: attendee emails, meeting title, description
    attendees = [att['email'] for att in event.get('attendees', [])]
    return event, attendees
```

### Drive Tool - Real API Calls

```python
# 1. Searching for meeting transcripts
def search_transcripts(self, meeting_title, meeting_date):
    query = f"name contains '{meeting_title}' and createdTime >= '{meeting_date}'"
    results = self.drive_service.files().list(
        q=query,
        fields="files(id, name, mimeType, createdTime, webViewLink)",
        pageSize=50
    ).execute()

    # Returns: Transcript files from your Drive
    return results.get('files', [])

# 2. Reading transcript content
def read_transcript(self, file_id):
    # For Google Docs
    content = self.drive_service.files().export_media(
        fileId=file_id,
        mimeType='text/plain'
    ).execute()

    # Returns: Full transcript text for AI processing
    return content.decode('utf-8')

# 3. Uploading meeting summary
def upload_summary(self, summary_content, meeting_title):
    file_metadata = {
        'name': f"{meeting_title}_Summary_{datetime.now().strftime('%Y%m%d')}.html",
        'parents': ['your_meeting_summaries_folder_id']
    }

    media = MediaFileUpload(
        io.BytesIO(summary_content.encode()),
        mimetype='text/html',
        resumable=True
    )

    file = self.drive_service.files().create(
        body=file_metadata,
        media_body=media
    ).execute()

    # Returns: File ID for calendar attachment
    return file['id']
```

### Email Service - Real API Calls

```python
# Sending meeting summary to attendees
def send_meeting_summary(self, attendee_email, meeting_title, summary_html):
    # Create email message
    message = MIMEMultipart('alternative')
    message['to'] = attendee_email
    message['from'] = '<EMAIL>'  # Your authenticated Gmail
    message['subject'] = f"Meeting Summary: {meeting_title}"

    # Add HTML content
    html_part = MIMEText(summary_html, 'html')
    message.attach(html_part)

    # Encode and send
    raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()

    result = self.gmail_service.users().messages().send(
        userId='me',
        body={'raw': raw_message}
    ).execute()

    # Returns: Message ID of sent email
    return result.get('id')
```

### Calendar Attachment Tool - Combined Operations

```python
# Complete flow: Upload to Drive + Attach to Calendar
def attach_summary_to_meeting(self, event_id, summary_file_path, meeting_title):
    # Step 1: Upload to your Google Drive
    file_metadata = {
        'name': f"{meeting_title}_Summary.html",
        'description': f"AI-generated summary for {meeting_title}"
    }

    media = MediaFileUpload(summary_file_path, mimetype='text/html')

    drive_file = self.drive_service.files().create(
        body=file_metadata,
        media_body=media
    ).execute()

    # Step 2: Attach to your calendar event
    attachment = {
        'fileId': drive_file['id'],
        'title': f"{meeting_title}_Summary.html",
        'mimeType': 'text/html'
    }

    updated_event = self.calendar_service.events().patch(
        calendarId='primary',
        eventId=event_id,
        body={'attachments': [attachment]}
    ).execute()

    return drive_file['id'], updated_event['id']
```

---

## 🔍 Real-World Example: Complete Authentication Flow

When you run the agent, here's exactly what happens with your authentication:

### 1. **Authentication Initialization**

```python
# Your credentials are loaded
auth = GoogleAuthenticator()
# Loads: ./keys/google-oauth-credentials.json
# Loads: ./keys/google-token.json
# Creates authenticated service instances
```

### 2. **Meeting Detection (Calendar API)**

```python
# Searches your calendar for recent meetings
calendar_tool.run("find meetings from the last 30 minutes")
# API Call: GET https://www.googleapis.com/calendar/v3/calendars/primary/events
# Authorization: Bearer YOUR_ACCESS_TOKEN
# Returns: Your meeting events with attendee lists
```

### 3. **Transcript Reading (Drive API)**

```python
# Searches your Drive for transcript files
drive_tool.run("find files for event 'Team Meeting' at 2025-07-28 15:30")
# API Call: GET https://www.googleapis.com/drive/v3/files
# Authorization: Bearer YOUR_ACCESS_TOKEN
# Returns: Transcript files from your Drive
```

### 4. **Email Sending (Gmail API)**

```python
# Sends summary from your Gmail account
email_service.send_email(
    to_email="<EMAIL>",
    subject="Meeting Summary: Team Meeting",
    html_body=ai_generated_summary
)
# API Call: POST https://www.googleapis.com/gmail/v1/users/me/messages/send
# Authorization: Bearer YOUR_ACCESS_TOKEN
# Sends: Email from your authenticated Gmail account
```

### 5. **File Storage (Drive API)**

```python
# Uploads summary to your Drive
drive_tool.upload_summary(summary_content, "Team Meeting")
# API Call: POST https://www.googleapis.com/upload/drive/v3/files
# Authorization: Bearer YOUR_ACCESS_TOKEN
# Creates: HTML/JSON summary files in your Drive
```

### 6. **Calendar Attachment (Calendar + Drive APIs)**

```python
# Attaches summary to your calendar event
attachment_tool.attach_to_event(event_id, summary_file_id)
# API Calls:
#   - PATCH https://www.googleapis.com/calendar/v3/calendars/primary/events/{eventId}
#   - Authorization: Bearer YOUR_ACCESS_TOKEN
# Result: Summary attached to your calendar event
```

---

## 🛡️ Security & Privacy Details

### What Your Authentication Enables:

- **Read Access**: Your calendar events, Drive files, Gmail profile
- **Write Access**: Create Drive files, send Gmail messages, modify calendar events
- **Scope Limitations**: Only the specific permissions you granted during OAuth
- **Data Ownership**: All data remains in YOUR Google account
- **Token Security**: Refresh tokens stored locally, access tokens auto-refreshed

### What the Agent CANNOT Do:

- Access other users' data (unless explicitly shared with you)
- Perform operations outside the granted scopes
- Store your data externally (everything stays in your Google Workspace)
- Access your data without valid authentication tokens
