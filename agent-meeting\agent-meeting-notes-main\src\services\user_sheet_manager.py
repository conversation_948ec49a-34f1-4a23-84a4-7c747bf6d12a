"""User Sheet Manager for centralized meeting task management."""

import os
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

try:
    from googleapiclient.errors import HttpError
except ImportError:
    HttpError = Exception

from src.services.utility.google_auth import GoogleAuthenticator

# Temporary file-based storage for testing when database is not available
TEMP_USER_SHEETS_FILE = "temp_user_sheets.json"

logger = logging.getLogger(__name__)


class UserSheetManager:
    """Manages centralized meeting_task sheets for authenticated users."""

    def __init__(self, authenticator: GoogleAuthenticator):
        """
        Initialize User Sheet Manager.
        
        Args:
            authenticator: Google authenticator instance
        """
        self.authenticator = authenticator
        self.sheets_service = authenticator.get_sheets_service()
        self.drive_service = authenticator.get_drive_service()
        
        if not self.sheets_service:
            logger.error("Failed to initialize Google Sheets service")

    def create_meeting_task_sheet_if_not_exists(self, user_email: str = None) -> Optional[str]:
        """
        Create a central meeting_task sheet for the user if it doesn't exist.
        
        Args:
            user_email: User email (if None, will get from auth)
            
        Returns:
            Sheet ID if successful, None otherwise
        """
        try:
            # Get user email if not provided
            if not user_email:
                user_email = self.authenticator.get_user_email()
                if not user_email:
                    logger.error("Could not determine user email")
                    return None

            # Check if user already has a sheet
            existing_sheet_id = self.get_user_sheet_id(user_email)
            if existing_sheet_id:
                logger.info(f"User {user_email} already has meeting_task sheet: {existing_sheet_id}")
                return existing_sheet_id

            # Create new sheet
            sheet_id = self._create_new_meeting_task_sheet(user_email)
            if sheet_id:
                # Store mapping in database
                self._store_user_sheet_mapping(user_email, sheet_id)
                logger.info(f"Created new meeting_task sheet for {user_email}: {sheet_id}")
                return sheet_id
            else:
                logger.error(f"Failed to create meeting_task sheet for {user_email}")
                return None

        except Exception as e:
            logger.error(f"Error creating meeting_task sheet: {e}")
            return None

    def get_user_sheet_id(self, user_email: str) -> Optional[str]:
        """
        Get the sheet ID for a user from storage (database or temp file).

        Args:
            user_email: User email address

        Returns:
            Sheet ID if found, None otherwise
        """
        try:
            # Try database first
            try:
                from src.constants.tables import UserSheetMapping
                from src.configuration.db import SessionLocal

                db = SessionLocal()
                try:
                    mapping = db.query(UserSheetMapping).filter(
                        UserSheetMapping.user_email == user_email
                    ).first()

                    if mapping:
                        # Update last used timestamp
                        mapping.last_used_at = datetime.now(timezone.utc)
                        db.commit()
                        return mapping.sheet_id
                    else:
                        return None

                finally:
                    db.close()

            except Exception as db_error:
                logger.warning(f"Database not available, using temp file storage: {db_error}")
                # Fall back to temp file storage
                return self._get_user_sheet_id_from_file(user_email)

        except Exception as e:
            logger.error(f"Error getting user sheet ID: {e}")
            return None

    def append_tasks_to_sheet(self, sheet_id: str, tasks: List[Dict[str, Any]]) -> bool:
        """
        Append tasks to the user's meeting_task sheet.
        
        Args:
            sheet_id: Google Sheets ID
            tasks: List of task dictionaries
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not tasks:
                logger.info("No tasks to append")
                return True

            # Convert tasks to rows
            rows = []
            for task in tasks:
                row = [
                    task.get('description', ''),
                    task.get('priority', 'MEDIUM'),
                    task.get('assigned_to', ''),
                    task.get('deadline', ''),
                    task.get('meeting_title', ''),
                    task.get('date_added', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                    task.get('status', 'Open'),
                    task.get('owner', ''),
                    task.get('context', '')[:200]  # Limit context length
                ]
                rows.append(row)

            # Append to sheet
            range_name = 'Sheet1!A:I'  # Append to columns A through I
            body = {
                'values': rows
            }

            self.sheets_service.spreadsheets().values().append(
                spreadsheetId=sheet_id,
                range=range_name,
                valueInputOption='RAW',
                body=body
            ).execute()

            logger.info(f"Appended {len(rows)} tasks to sheet {sheet_id}")
            return True

        except HttpError as e:
            logger.error(f"HTTP error appending tasks to sheet: {e}")
            return False
        except Exception as e:
            logger.error(f"Error appending tasks to sheet: {e}")
            return False

    def extract_tasks_from_summary(self, json_output: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract tasks from AI summary JSON output.

        Args:
            json_output: AI summarizer output

        Returns:
            List of task dictionaries
        """
        try:
            tasks = []
            meeting_title = json_output.get('meeting_title', 'Unknown Meeting')
            date_added = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Handle both 'outcomes' format (from real AI) and 'action_items' format (from tests)
            action_items = json_output.get('action_items', [])
            if action_items:
                # Direct action_items format
                for action in action_items:
                    task = {
                        'description': action.get('task', 'No task description'),
                        'priority': action.get('priority', 'MEDIUM'),
                        'assigned_to': action.get('owner', 'Unknown'),
                        'deadline': action.get('deadline', 'Not specified'),
                        'meeting_title': meeting_title,
                        'date_added': date_added,
                        'status': 'Open',
                        'owner': action.get('owner', 'Unknown'),
                        'context': action.get('context', 'No context')[:200] if action.get('context') else 'No context'
                    }
                    tasks.append(task)
            else:
                # Original outcomes format
                outcomes = json_output.get('outcomes', [])
                for outcome in outcomes:
                    decision = outcome.get('decision', 'No decision')
                    context = outcome.get('context', 'No context')
                    owner = outcome.get('owner', 'Unknown')

                    actions = outcome.get('actions', [])
                    for action in actions:
                        task = {
                            'description': action.get('task', 'No task description'),
                            'priority': action.get('priority', 'MEDIUM'),
                            'assigned_to': action.get('owner', owner),
                            'deadline': action.get('deadline', 'Not specified'),
                            'meeting_title': meeting_title,
                            'date_added': date_added,
                            'status': 'Open',
                            'owner': owner,
                            'context': f"{decision} | {context}"[:200]
                        }
                        tasks.append(task)

            logger.info(f"Extracted {len(tasks)} tasks from AI summary")
            return tasks

        except Exception as e:
            logger.error(f"Error extracting tasks from summary: {e}")
            return []

    def _create_new_meeting_task_sheet(self, user_email: str) -> Optional[str]:
        """Create a new meeting_task Google Sheet."""
        # user_email parameter available for future customization
        try:
            # Create spreadsheet
            spreadsheet_body = {
                'properties': {
                    'title': 'meeting_task'
                },
                'sheets': [{
                    'properties': {
                        'title': 'Sheet1'
                    }
                }]
            }

            spreadsheet = self.sheets_service.spreadsheets().create(
                body=spreadsheet_body
            ).execute()

            sheet_id = spreadsheet['spreadsheetId']
            # sheet_url = spreadsheet['spreadsheetUrl']  # Available if needed

            # Add headers
            headers = [
                'Task Description', 'Priority', 'Assigned To', 'Deadline',
                'Meeting Title', 'Date Added', 'Status', 'Owner', 'Context'
            ]

            header_body = {
                'values': [headers]
            }

            self.sheets_service.spreadsheets().values().update(
                spreadsheetId=sheet_id,
                range='Sheet1!A1:I1',
                valueInputOption='RAW',
                body=header_body
            ).execute()

            # Format headers (bold) - Get the actual sheet ID first
            sheet_metadata = self.sheets_service.spreadsheets().get(
                spreadsheetId=sheet_id
            ).execute()

            actual_sheet_id = sheet_metadata['sheets'][0]['properties']['sheetId']

            format_body = {
                'requests': [{
                    'repeatCell': {
                        'range': {
                            'sheetId': actual_sheet_id,
                            'startRowIndex': 0,
                            'endRowIndex': 1,
                            'startColumnIndex': 0,
                            'endColumnIndex': 9
                        },
                        'cell': {
                            'userEnteredFormat': {
                                'textFormat': {
                                    'bold': True
                                }
                            }
                        },
                        'fields': 'userEnteredFormat.textFormat.bold'
                    }
                }]
            }

            self.sheets_service.spreadsheets().batchUpdate(
                spreadsheetId=sheet_id,
                body=format_body
            ).execute()

            logger.info(f"Created meeting_task sheet: {sheet_id}")
            return sheet_id

        except Exception as e:
            logger.error(f"Error creating meeting_task sheet: {e}")
            return None

    def _store_user_sheet_mapping(self, user_email: str, sheet_id: str) -> bool:
        """Store user-sheet mapping in database or temp file."""
        try:
            # Try database first
            try:
                from src.constants.tables import UserSheetMapping
                from src.configuration.db import SessionLocal

                db = SessionLocal()
                try:
                    # Check if mapping already exists
                    existing = db.query(UserSheetMapping).filter(
                        UserSheetMapping.user_email == user_email
                    ).first()

                    if existing:
                        # Update existing mapping
                        existing.sheet_id = sheet_id
                        existing.updated_at = datetime.now(timezone.utc)
                        existing.last_used_at = datetime.now(timezone.utc)
                    else:
                        # Create new mapping
                        mapping = UserSheetMapping(
                            user_email=user_email,
                            sheet_id=sheet_id,
                            sheet_name='meeting_task',
                            sheet_url=f'https://docs.google.com/spreadsheets/d/{sheet_id}',
                            created_at=datetime.now(timezone.utc),
                            updated_at=datetime.now(timezone.utc),
                            last_used_at=datetime.now(timezone.utc)
                        )
                        db.add(mapping)

                    db.commit()
                    logger.info(f"Stored user sheet mapping in database: {user_email} -> {sheet_id}")
                    return True

                finally:
                    db.close()

            except Exception as db_error:
                logger.warning(f"Database not available, using temp file storage: {db_error}")
                # Fall back to temp file storage
                return self._store_user_sheet_mapping_to_file(user_email, sheet_id)

        except Exception as e:
            logger.error(f"Error storing user sheet mapping: {e}")
            return False

    def test_connection(self) -> bool:
        """Test the connection and user authentication."""
        try:
            if not self.sheets_service:
                return False

            # Get user email to verify authentication
            user_email = self.authenticator.get_user_email()
            if not user_email:
                logger.error("Could not get user email for connection test")
                return False

            logger.info(f"User Sheet Manager connection test passed for {user_email}")
            return True

        except Exception as e:
            logger.error(f"User Sheet Manager connection test failed: {e}")
            return False

    def _get_user_sheet_id_from_file(self, user_email: str) -> Optional[str]:
        """Get user sheet ID from temporary file storage."""
        try:
            if not os.path.exists(TEMP_USER_SHEETS_FILE):
                return None

            with open(TEMP_USER_SHEETS_FILE, 'r') as f:
                data = json.load(f)

            mapping = data.get(user_email)
            if mapping:
                logger.info(f"Retrieved user sheet mapping from file: {user_email} -> {mapping['sheet_id']}")
                return mapping['sheet_id']
            else:
                return None

        except Exception as e:
            logger.error(f"Error reading user sheet mapping from file: {e}")
            return None

    def _store_user_sheet_mapping_to_file(self, user_email: str, sheet_id: str) -> bool:
        """Store user sheet mapping to temporary file storage."""
        try:
            # Load existing data
            data = {}
            if os.path.exists(TEMP_USER_SHEETS_FILE):
                try:
                    with open(TEMP_USER_SHEETS_FILE, 'r') as f:
                        data = json.load(f)
                except:
                    data = {}

            # Update mapping
            data[user_email] = {
                'sheet_id': sheet_id,
                'sheet_name': 'meeting_task',
                'sheet_url': f'https://docs.google.com/spreadsheets/d/{sheet_id}',
                'created_at': datetime.now(timezone.utc).isoformat(),
                'updated_at': datetime.now(timezone.utc).isoformat(),
                'last_used_at': datetime.now(timezone.utc).isoformat()
            }

            # Save to file
            with open(TEMP_USER_SHEETS_FILE, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Stored user sheet mapping to file: {user_email} -> {sheet_id}")
            return True

        except Exception as e:
            logger.error(f"Error storing user sheet mapping to file: {e}")
            return False
