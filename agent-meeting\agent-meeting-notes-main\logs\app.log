2025-08-01 09:01:11 - __main__ - INFO - ============================================================
2025-08-01 09:01:11 - src.services.utility.google_auth - INFO - Loaded existing OAuth2 token
2025-08-01 09:01:11 - src.services.utility.google_auth - INFO - Refreshing expired OAuth2 token
2025-08-01 09:01:12 - src.services.utility.google_auth - INFO - OAuth2 token saved to ./keys/google-token.json
2025-08-01 09:01:12 - src.services.utility.google_auth - INFO - Google OAuth2 credentials loaded successfully
2025-08-01 09:01:12 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-08-01 09:01:12 - src.services.utility.google_auth - INFO - Gmail service created successfully with OAuth2
2025-08-01 09:01:13 - src.services.utility.google_auth - INFO - Gmail API OAuth2 authentication test passed
2025-08-01 09:01:13 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-08-01 09:01:13 - src.services.utility.google_auth - INFO - Calendar service created successfully with OAuth2
2025-08-01 09:01:14 - src.services.utility.google_auth - INFO - Calendar API OAuth2 authentication test passed
2025-08-01 09:01:14 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-08-01 09:01:14 - src.services.utility.google_auth - INFO - Drive service created successfully with OAuth2
2025-08-01 09:01:15 - src.services.utility.google_auth - INFO - Drive API OAuth2 authentication test passed
2025-08-01 09:01:15 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-08-01 09:01:15 - src.services.utility.google_auth - INFO - Sheets service created successfully with OAuth2
2025-08-01 09:01:17 - src.services.utility.google_auth - INFO - Sheets API OAuth2 authentication test passed
2025-08-01 09:01:17 - src.services.utility.google_auth - INFO - All Google services OAuth2 authentication tests passed
2025-08-01 09:01:17 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-08-01 09:01:17 - src.services.utility.google_auth - INFO - Gmail service created successfully with OAuth2
2025-08-01 09:01:18 - src.services.utility.google_auth - INFO - Retrieved user email: <EMAIL>
2025-08-01 09:01:18 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-08-01 09:01:18 - src.services.utility.google_auth - INFO - Sheets service created successfully with OAuth2
2025-08-01 09:01:18 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-08-01 09:01:18 - src.services.utility.google_auth - INFO - Drive service created successfully with OAuth2
2025-08-01 09:01:18 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-08-01 09:01:18 - src.services.utility.google_auth - INFO - Gmail service created successfully with OAuth2
2025-08-01 09:01:19 - src.services.utility.google_auth - INFO - Retrieved user email: <EMAIL>
2025-08-01 09:01:19 - src.services.user_sheet_manager - INFO - User Sheet Manager connection test <NAME_EMAIL>
2025-08-01 09:01:19 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-08-01 09:01:19 - src.services.utility.google_auth - INFO - Gmail service created successfully with OAuth2
2025-08-01 09:01:20 - src.services.utility.google_auth - INFO - Retrieved user email: <EMAIL>
2025-08-01 09:01:30 - src.services.user_sheet_manager - ERROR - Error getting user sheet ID: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '34.27.185.225' (timed out)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 09:01:34 - src.services.user_sheet_manager - ERROR - Error creating meeting_task sheet: <HttpError 400 when requesting https://sheets.googleapis.com/v4/spreadsheets/1eHUPnWPkUBbTSqyVWs9voBWDascw-_7pkofc0XMu80M:batchUpdate?alt=json returned "Invalid requests[0].repeatCell: No grid with id: 0". Details: "Invalid requests[0].repeatCell: No grid with id: 0">
2025-08-01 09:01:34 - src.services.user_sheet_manager - ERROR - Failed to create meeting_task <NAME_EMAIL>
2025-08-01 11:35:28 - src.services.user_sheet_manager - WARNING - Database not available, using temp file storage: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '34.27.185.225' (timed out)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 11:35:28 - src.services.user_sheet_manager - INFO - Retrieved user sheet mapping from file: <EMAIL> -> 1HJwwMD9ZceiuA4H4YnQvMbVwwpmEVvmf5UHrt_3bSco
2025-08-01 11:35:28 - src.services.user_sheet_manager - INFO - User <EMAIL> already has meeting_task sheet: 1HJwwMD9ZceiuA4H4YnQvMbVwwpmEVvmf5UHrt_3bSco
2025-08-01 11:35:31 - src.services.user_sheet_manager - INFO - Appended 1 tasks to sheet 1HJwwMD9ZceiuA4H4YnQvMbVwwpmEVvmf5UHrt_3bSco
2025-08-01 11:37:24 - src.services.user_sheet_manager - WARNING - Database not available, using temp file storage: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '34.27.185.225' (timed out)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 11:37:24 - src.services.user_sheet_manager - INFO - Retrieved user sheet mapping from file: <EMAIL> -> 1HJwwMD9ZceiuA4H4YnQvMbVwwpmEVvmf5UHrt_3bSco
2025-08-01 11:37:24 - src.services.user_sheet_manager - INFO - User <EMAIL> already has meeting_task sheet: 1HJwwMD9ZceiuA4H4YnQvMbVwwpmEVvmf5UHrt_3bSco
2025-08-01 11:37:34 - src.services.user_sheet_manager - WARNING - Database not available, using temp file storage: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '34.27.185.225' (timed out)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 11:37:34 - src.services.user_sheet_manager - INFO - Retrieved user sheet mapping from file: <EMAIL> -> 1HJwwMD9ZceiuA4H4YnQvMbVwwpmEVvmf5UHrt_3bSco
2025-08-01 11:37:34 - src.services.user_sheet_manager - INFO - User <EMAIL> already has meeting_task sheet: 1HJwwMD9ZceiuA4H4YnQvMbVwwpmEVvmf5UHrt_3bSco
2025-08-01 11:37:34 - src.services.user_sheet_manager - INFO - Extracted 0 tasks from AI summary
2025-08-01 11:37:34 - src.services.google_sheets_service - INFO - No tasks found in AI output
2025-08-01 11:40:15 - src.services.user_sheet_manager - WARNING - Database not available, using temp file storage: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '34.27.185.225' (timed out)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 11:40:15 - src.services.user_sheet_manager - INFO - Retrieved user sheet mapping from file: <EMAIL> -> 1HJwwMD9ZceiuA4H4YnQvMbVwwpmEVvmf5UHrt_3bSco
2025-08-01 11:40:15 - src.services.user_sheet_manager - INFO - User <EMAIL> already has meeting_task sheet: 1HJwwMD9ZceiuA4H4YnQvMbVwwpmEVvmf5UHrt_3bSco
2025-08-01 11:40:25 - src.services.user_sheet_manager - WARNING - Database not available, using temp file storage: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '34.27.185.225' (timed out)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 11:40:25 - src.services.user_sheet_manager - INFO - Retrieved user sheet mapping from file: <EMAIL> -> 1HJwwMD9ZceiuA4H4YnQvMbVwwpmEVvmf5UHrt_3bSco
2025-08-01 11:40:25 - src.services.user_sheet_manager - INFO - User <EMAIL> already has meeting_task sheet: 1HJwwMD9ZceiuA4H4YnQvMbVwwpmEVvmf5UHrt_3bSco
2025-08-01 11:40:25 - src.services.user_sheet_manager - INFO - Extracted 5 tasks from AI summary
2025-08-01 11:40:26 - src.services.user_sheet_manager - INFO - Appended 5 tasks to sheet 1HJwwMD9ZceiuA4H4YnQvMbVwwpmEVvmf5UHrt_3bSco
2025-08-01 11:40:26 - src.services.google_sheets_service - INFO - Successfully wrote 5 tasks to user's m eeting_task sheet
