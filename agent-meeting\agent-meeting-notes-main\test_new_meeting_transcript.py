"""Test centralized sheets with a new meeting transcript simulation."""

import os
import sys
import json

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

print("🚀 Testing Centralized Sheets with New Meeting Transcript")
print("=" * 60)

try:
    print("📦 Importing modules...")
    from src.services.utility.google_auth import GoogleAuthenticator
    from src.services.user_sheet_manager import UserSheetManager
    from src.services.google_sheets_service import GoogleSheetsService
    print("✅ Modules imported successfully")
    
    print("\n🔐 Initializing authentication...")
    auth = GoogleAuthenticator()
    user_email = auth.get_user_email()
    print(f"👤 User email: {user_email}")
    
    print("\n📊 Initializing services...")
    sheet_manager = UserSheetManager(auth)
    sheets_service = GoogleSheetsService(auth)
    print("✅ Services initialized")
    
    # Simulate AI output from a new meeting transcript
    print("\n📝 Creating sample AI output from 'Marketing Campaign Review' meeting...")
    ai_output = {
        "meeting_title": "Marketing Campaign Review - Q1 2024",
        "meeting_date": "2024-01-16",
        "attendees": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
        "summary": "Reviewed Q1 marketing campaign performance and planned Q2 initiatives. Discussed budget allocation, content strategy, and performance metrics.",
        "decisions": [
            {
                "decision": "Increase social media advertising budget by 25% for Q2",
                "context": "Q1 social media campaigns showed 40% higher ROI than expected"
            },
            {
                "decision": "Launch influencer partnership program in March",
                "context": "Competitor analysis shows strong potential for brand awareness growth"
            }
        ],
        "action_items": [
            {
                "task": "Prepare Q2 social media advertising budget proposal",
                "owner": "Jennifer Smith",
                "deadline": "2024-01-25",
                "priority": "HIGH",
                "context": "Need executive approval for 25% budget increase"
            },
            {
                "task": "Research and identify potential influencer partners",
                "owner": "Maria Garcia",
                "deadline": "2024-02-05",
                "priority": "HIGH",
                "context": "Focus on tech and business influencers with 50K+ followers"
            },
            {
                "task": "Create content calendar for Q2 campaigns",
                "owner": "Robert Johnson",
                "deadline": "2024-02-10",
                "priority": "MEDIUM",
                "context": "Align with product launch dates and seasonal trends"
            },
            {
                "task": "Set up tracking and analytics for influencer campaigns",
                "owner": "Alex Chen",
                "deadline": "2024-02-15",
                "priority": "MEDIUM",
                "context": "Need to measure ROI and engagement metrics"
            },
            {
                "task": "Draft influencer partnership contracts and guidelines",
                "owner": "Legal Team",
                "deadline": "2024-02-20",
                "priority": "MEDIUM",
                "context": "Ensure compliance and clear deliverable expectations"
            }
        ]
    }
    
    print(f"📋 Meeting: {ai_output['meeting_title']}")
    print(f"📅 Date: {ai_output['meeting_date']}")
    print(f"👥 Attendees: {', '.join(ai_output['attendees'])}")
    print(f"📋 Tasks to extract: {len(ai_output['action_items'])}")
    
    print("\n📊 Ensuring centralized meeting_task sheet exists...")
    sheet_id = sheets_service.ensure_user_meeting_task_sheet(user_email)
    print(f"✅ Sheet ID: {sheet_id}")
    
    print("\n💾 Writing tasks from AI summary to centralized sheet...")
    success = sheets_service.write_tasks_from_ai_summary(ai_output, user_email)
    
    if success:
        print("✅ Tasks written successfully to centralized meeting_task sheet!")
        
        print("\n📋 Tasks added to your centralized sheet:")
        for i, task in enumerate(ai_output['action_items'], 1):
            print(f"  {i}. {task['task']}")
            print(f"     👤 Owner: {task['owner']}")
            print(f"     📅 Deadline: {task['deadline']}")
            print(f"     🔥 Priority: {task['priority']}")
            print()
        
        print("🎉 SUCCESS! New meeting tasks added to existing centralized sheet!")
        print(f"🔗 Your centralized sheet: https://docs.google.com/spreadsheets/d/{sheet_id}")
        print("\n💡 Key Points:")
        print("  ✅ Used the SAME existing meeting_task sheet")
        print("  ✅ Added 5 new tasks from Marketing Campaign Review meeting")
        print("  ✅ Previous tasks from other meetings are still there")
        print("  ✅ All tasks are centralized in one location")
        
    else:
        print("❌ Failed to write tasks to centralized sheet")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 60)
print("🏁 Test completed - Check your Google Sheet to see the new tasks!")
