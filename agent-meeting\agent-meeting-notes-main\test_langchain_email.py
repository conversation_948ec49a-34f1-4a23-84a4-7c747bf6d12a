#!/usr/bin/env python3
"""
Test LangChain Notification Tool Email Integration
"""

import sys
import os
from datetime import datetime

# Add src to path
sys.path.append('src')

# Load environment
from dotenv import load_dotenv
load_dotenv()

from src.tools.langchain_notification_tool import NotificationTool
from src.services.utility.google_auth import GoogleAuthenticator
from src.services.email_service import EmailService
from src.services.notification_service import NotificationService

def test_langchain_notification_tool():
    """Test the LangChain notification tool."""
    print("🧪 Testing LangChain Notification Tool")
    print("=" * 50)
    
    try:
        # Initialize services
        auth = GoogleAuthenticator()
        email_service = EmailService(provider="gmail", google_auth=auth)
        notification_service = NotificationService(email_service=email_service)
        
        # Initialize notification tool
        notification_tool = NotificationTool(notification_service=notification_service)
        
        # Test query with meeting summary
        test_query = """
Send meeting <NAME_EMAIL> for meeting titled "Test Agent Meeting".

Meeting Summary:
<h2>🤖 Test Agent Meeting Summary</h2>
<p>This is a test meeting summary generated by the Meeting Intelligence Agent to verify email integration.</p>

<h3>📋 Key Outcomes:</h3>
<ul>
<li>✅ Email functionality verified and working</li>
<li>✅ Agent integration operational</li>
<li>✅ Notification tool successfully integrated</li>
<li>✅ LangChain workflow complete</li>
</ul>

<h3>🎯 Action Items:</h3>
<ul>
<li>Continue testing other agent features</li>
<li>Verify Google Sheets integration</li>
<li>Test complete workflow end-to-end</li>
</ul>

<p><strong>Meeting Date:</strong> {}</p>
<p><strong>Generated by:</strong> Meeting Intelligence Agent v1.0</p>
        """.format(datetime.now().strftime('%B %d, %Y'))
        
        print("📧 Sending test email via LangChain notification tool...")
        result = notification_tool._run(test_query)
        
        print(f"✅ LangChain tool completed!")
        print(f"📊 Result: {result}")
        
        # Check if the result indicates success
        success = "success" in result.lower() and "error" not in result.lower()
        return success
        
    except Exception as e:
        print(f"❌ LangChain notification tool test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_workflow_step4():
    """Test Step 4 of the agent workflow (email notification)."""
    print("\n🧪 Testing Agent Workflow Step 4 (Email Notification)")
    print("=" * 50)
    
    try:
        # This simulates what the agent would do in Step 4
        from src.agents.langchain_meeting_agent import MeetingIntelligenceAgent
        
        # Initialize agent
        agent = MeetingIntelligenceAgent()
        
        # Test Step 4 specifically
        step4_query = """
Execute Step 4: Send meeting summary <NAME_EMAIL>.

Meeting Title: Agent Integration Test Meeting
Attendees: <EMAIL>
Meeting Date: {}

Summary Content:
<h2>🤖 Agent Integration Test Meeting</h2>
<p>This meeting summary was generated to test the complete agent workflow, specifically Step 4 (email notification).</p>

<h3>📋 Meeting Outcomes:</h3>
<ul>
<li>✅ All 7 agent tools integrated successfully</li>
<li>✅ Email notification system operational</li>
<li>✅ Google Sheets integration working</li>
<li>✅ OAuth authentication functioning properly</li>
</ul>

<h3>🎯 Next Steps:</h3>
<ul>
<li>Complete end-to-end workflow testing</li>
<li>Verify all integrations are stable</li>
<li>Document final system status</li>
</ul>
        """.format(datetime.now().strftime('%B %d, %Y'))
        
        print("🤖 Running agent Step 4 test...")
        
        # Get the notification tool from the agent
        notification_tool = None
        for tool in agent.tools:
            if hasattr(tool, 'name') and 'notification' in tool.name.lower():
                notification_tool = tool
                break
        
        if notification_tool:
            result = notification_tool._run(step4_query)
            print(f"✅ Agent Step 4 completed!")
            print(f"📊 Result: {result}")
            
            success = "success" in result.lower() and "error" not in result.lower()
            return success
        else:
            print("❌ Notification tool not found in agent")
            return False
        
    except Exception as e:
        print(f"❌ Agent workflow Step 4 test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 LangChain Email Integration Test")
    print("=" * 60)
    
    # Test 1: LangChain notification tool
    langchain_success = test_langchain_notification_tool()
    
    # Test 2: Agent workflow Step 4
    agent_success = test_agent_workflow_step4()
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    print(f"LangChain Tool:       {'✅ PASSED' if langchain_success else '❌ FAILED'}")
    print(f"Agent Workflow Step 4: {'✅ PASSED' if agent_success else '❌ FAILED'}")
    
    if langchain_success and agent_success:
        print("\n🎉 ALL LANGCHAIN EMAIL TESTS PASSED!")
        print("📧 Email integration with LangChain agent is working perfectly!")
        print("✅ Step 4 of the agent workflow (email notification) is operational!")
    else:
        print("\n⚠️  Some tests failed - check the error messages above")
