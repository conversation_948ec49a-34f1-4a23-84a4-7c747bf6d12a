#!/usr/bin/env python3
"""
Fix TLS certificate issues for Google API authentication.
"""

import os
import sys
import ssl
import certifi
import requests

def fix_ssl_certificates():
    """Fix SSL certificate configuration."""
    print("🔧 Fixing SSL Certificate Configuration...")
    
    # Method 1: Use certifi bundle
    try:
        cert_path = certifi.where()
        print(f"✅ Found certifi bundle: {cert_path}")
        
        # Set environment variables
        os.environ['SSL_CERT_FILE'] = cert_path
        os.environ['REQUESTS_CA_BUNDLE'] = cert_path
        os.environ['CURL_CA_BUNDLE'] = cert_path
        
        print("✅ Set SSL environment variables")
        
        # Test SSL connection
        response = requests.get('https://www.googleapis.com', timeout=10)
        print(f"✅ SSL test successful: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ SSL fix failed: {e}")
        return False

def test_google_api_connection():
    """Test connection to Google APIs."""
    print("\n🌐 Testing Google API Connection...")
    
    try:
        # Test OAuth endpoint
        response = requests.get('https://oauth2.googleapis.com/.well-known/openid_configuration', timeout=10)
        print(f"✅ OAuth endpoint: {response.status_code}")
        
        # Test Gmail API
        response = requests.get('https://gmail.googleapis.com/$discovery/rest?version=v1', timeout=10)
        print(f"✅ Gmail API: {response.status_code}")
        
        # Test Calendar API
        response = requests.get('https://www.googleapis.com/discovery/v1/apis/calendar/v3/rest', timeout=10)
        print(f"✅ Calendar API: {response.status_code}")
        
        # Test Drive API
        response = requests.get('https://www.googleapis.com/discovery/v1/apis/drive/v3/rest', timeout=10)
        print(f"✅ Drive API: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ API connection test failed: {e}")
        return False

def create_ssl_context():
    """Create a proper SSL context."""
    print("\n🔐 Creating SSL Context...")
    
    try:
        # Create SSL context with proper certificate verification
        context = ssl.create_default_context(cafile=certifi.where())
        print("✅ SSL context created")
        
        # Test the context
        import socket
        with socket.create_connection(('www.googleapis.com', 443), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname='www.googleapis.com') as ssock:
                print(f"✅ SSL handshake successful: {ssock.version()}")
        
        return True
        
    except Exception as e:
        print(f"❌ SSL context test failed: {e}")
        return False

def update_environment_file():
    """Update .env file with SSL configuration."""
    print("\n📝 Updating Environment Configuration...")
    
    try:
        env_path = '.env'
        cert_path = certifi.where()
        
        # Read existing .env
        env_lines = []
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                env_lines = f.readlines()
        
        # Remove existing SSL settings
        env_lines = [line for line in env_lines if not any(
            ssl_var in line for ssl_var in ['SSL_CERT_FILE', 'REQUESTS_CA_BUNDLE', 'CURL_CA_BUNDLE']
        )]
        
        # Add SSL configuration
        ssl_config = [
            '\n# SSL Certificate Configuration\n',
            f'SSL_CERT_FILE={cert_path}\n',
            f'REQUESTS_CA_BUNDLE={cert_path}\n',
            f'CURL_CA_BUNDLE={cert_path}\n'
        ]
        
        env_lines.extend(ssl_config)
        
        # Write updated .env
        with open(env_path, 'w') as f:
            f.writelines(env_lines)
        
        print(f"✅ Updated {env_path} with SSL configuration")
        return True
        
    except Exception as e:
        print(f"❌ Failed to update environment file: {e}")
        return False

def main():
    """Main SSL certificate fix."""
    print("🚀 SSL Certificate Fix for Meeting Intelligence Agent")
    print("=" * 55)
    
    # Step 1: Fix SSL certificates
    ssl_fixed = fix_ssl_certificates()
    
    if ssl_fixed:
        # Step 2: Test Google API connections
        api_test = test_google_api_connection()
        
        # Step 3: Create SSL context
        ssl_context = create_ssl_context()
        
        # Step 4: Update environment file
        env_updated = update_environment_file()
        
        # Summary
        print("\n📊 SSL Fix Summary")
        print("=" * 25)
        print(f"SSL Configuration: {'✅ SUCCESS' if ssl_fixed else '❌ FAILED'}")
        print(f"API Connections: {'✅ SUCCESS' if api_test else '❌ FAILED'}")
        print(f"SSL Context: {'✅ SUCCESS' if ssl_context else '❌ FAILED'}")
        print(f"Environment Update: {'✅ SUCCESS' if env_updated else '❌ FAILED'}")
        
        if all([ssl_fixed, api_test, ssl_context, env_updated]):
            print("\n🎉 SSL CERTIFICATE ISSUES FIXED!")
            print("You can now run the authentication setup again.")
            
            # Show the certificate path
            print(f"\n📋 Certificate bundle location: {certifi.where()}")
            print("\n🔄 Next steps:")
            print("1. Run: python setup_authentication.py")
            print("2. Complete the OAuth flow in your browser")
            print("3. Test the agent functionality")
            
            return True
        else:
            print("\n⚠️  Some SSL issues remain.")
            return False
    else:
        print("\n❌ SSL certificate fix failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
