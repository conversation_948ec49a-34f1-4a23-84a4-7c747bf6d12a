"""
LangChain tool for attaching meeting summaries to calendar events.
"""

import json
import logging
import os
from datetime import datetime, timedelta
from typing import Optional, Any

from langchain.tools import BaseTool
from langchain.callbacks.manager import CallbackManagerForToolRun
from pydantic import BaseModel, Field

from src.services.utility.google_auth import GoogleAuthenticator
from src.services.utility.calendar_service import GoogleCalendarService
from src.constants.app import AVAILABLE_TOOLS

logger = logging.getLogger(__name__)


class CalendarAttachmentInput(BaseModel):
    """Input for calendar attachment operations."""
    event_query: str = Field(description="Query to find the calendar event (e.g., 'product-testing meeting today')")
    file_path: str = Field(description="Path to the HTML file to attach")
    attachment_title: str = Field(default="Meeting Summary", description="Display title for the attachment")


class CalendarAttachmentTool(BaseTool):
    """
    LangChain tool for attaching meeting summaries to calendar events.
    
    This tool allows the agent to:
    - Find calendar events based on meeting title and date
    - Upload HTML summary files to Google Drive
    - Attach the uploaded files to the corresponding calendar events
    """
    
    name: str = "calendar_attachment_tool"
    description: str = """
    Attach meeting summary HTML files to calendar events. 
    Use this tool to link generated meeting summaries back to their original calendar events.
    
    Input format: 'attach file path/to/summary.html to event "meeting title" from today'
    """
    category: str = "calendar"

    # Declare services as class variables to avoid Pydantic validation issues
    auth: Optional[GoogleAuthenticator] = None
    calendar_service: Optional[GoogleCalendarService] = None
    drive_service: Optional[Any] = None

    def __init__(self, auth: GoogleAuthenticator, **kwargs):
        super().__init__(**kwargs)
        self.auth = auth
        self.calendar_service = GoogleCalendarService(auth)
        
        # Initialize Drive service for file upload
        from googleapiclient.discovery import build
        self.drive_service = build('drive', 'v3', credentials=auth.credentials)
    
    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute calendar attachment operations."""
        try:
            logger.info(f"Processing calendar attachment request: {query}")
            
            # Parse the query to extract file path and event details
            import re
            
            # Expected format: "attach file path/to/file.html to event 'meeting title' from today/date"
            file_match = re.search(r'attach file ([^\s]+(?:\s+[^\s]+)*\.html)', query, re.IGNORECASE)
            event_match = re.search(r'to event ["\']([^"\']+)["\']', query, re.IGNORECASE)
            date_match = re.search(r'from (today|yesterday|\d{4}-\d{2}-\d{2})', query, re.IGNORECASE)
            
            if not file_match:
                return "Error: Please specify an HTML file path. Format: 'attach file path/to/summary.html to event \"meeting title\" from today'"
            
            if not event_match:
                return "Error: Please specify an event title. Format: 'attach file path/to/summary.html to event \"meeting title\" from today'"
            
            file_path = file_match.group(1).strip()
            event_title = event_match.group(1).strip()
            date_str = date_match.group(1).strip() if date_match else "today"
            
            # Check if file exists
            if not os.path.exists(file_path):
                return f"Error: File not found: {file_path}"
            
            # Parse date
            if date_str.lower() == "today":
                search_date = datetime.now()
            elif date_str.lower() == "yesterday":
                search_date = datetime.now() - timedelta(days=1)
            else:
                try:
                    search_date = datetime.strptime(date_str, "%Y-%m-%d")
                except ValueError:
                    return f"Error: Invalid date format: {date_str}. Use 'today', 'yesterday', or YYYY-MM-DD"
            
            # Step 1: Find the calendar event
            event_id = self._find_calendar_event(event_title, search_date)
            if not event_id:
                return f"Error: Could not find calendar event '{event_title}' on {date_str}"
            
            # Step 2: Upload file to Google Drive
            drive_file_info = self._upload_to_drive(file_path, event_title)
            if not drive_file_info:
                return f"Error: Failed to upload file to Google Drive: {file_path}"
            
            # Step 3: Attach file to calendar event
            success = self._attach_to_calendar(event_id, drive_file_info)
            if not success:
                return f"Error: Failed to attach file to calendar event"
            
            return json.dumps({
                "status": "success",
                "message": f"Successfully attached meeting summary to calendar event",
                "event_title": event_title,
                "event_id": event_id,
                "file_path": file_path,
                "drive_file_id": drive_file_info["id"],
                "drive_file_name": drive_file_info["name"],
                "attachment_url": drive_file_info["webViewLink"]
            }, indent=2)

        except Exception as e:
            logger.error(f"Calendar attachment tool error: {e}")
            return f"Error attaching file to calendar: {str(e)}"
    
    async def _arun(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute calendar attachment operations asynchronously."""
        return self._run(query, run_manager)
    
    def _find_calendar_event(self, event_title: str, search_date: datetime) -> Optional[str]:
        """Find calendar event by title and date."""
        try:
            # Search for events on the specified date
            start_time = search_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = start_time + timedelta(days=1)

            events = self.calendar_service.get_events_in_range(
                start_time=start_time,
                end_time=end_time
            )

            # Find event with matching title (case-insensitive, partial match)
            for event in events:
                event_summary = event.summary.lower()
                if event_title.lower() in event_summary or event_summary in event_title.lower():
                    logger.info(f"Found matching event: {event.summary} (ID: {event.id})")
                    return event.id

            logger.warning(f"No matching event found for '{event_title}' on {search_date.date()}")
            return None

        except Exception as e:
            logger.error(f"Error finding calendar event: {e}")
            return None
    
    def _upload_to_drive(self, file_path: str, event_title: str) -> Optional[dict]:
        """Upload HTML file to Google Drive."""
        try:
            import os
            from io import BytesIO
            from googleapiclient.http import MediaIoBaseUpload
            
            file_name = os.path.basename(file_path)
            
            # Create a folder for meeting summaries if it doesn't exist
            folder_id = self._find_or_create_folder("Meeting Summaries")
            
            # Read file content
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # Create file metadata
            file_metadata = {
                'name': f"{event_title} - {file_name}",
                'parents': [folder_id],
                'description': f"Meeting summary for {event_title}"
            }
            
            # Upload file
            media = MediaIoBaseUpload(BytesIO(file_content), mimetype='text/html', resumable=True)
            
            uploaded_file = self.drive_service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id,name,webViewLink,mimeType'
            ).execute()
            
            logger.info(f"Successfully uploaded {file_name} to Google Drive")
            return uploaded_file
            
        except Exception as e:
            logger.error(f"Error uploading to Drive: {e}")
            return None
    
    def _attach_to_calendar(self, event_id: str, drive_file_info: dict) -> bool:
        """Attach Drive file to calendar event."""
        try:
            success = self.calendar_service.add_attachment_to_event(
                event_id=event_id,
                file_id=drive_file_info['id'],
                title=drive_file_info['name'],
                mime_type=drive_file_info['mimeType'],
                web_view_link=drive_file_info['webViewLink']
            )
            
            if success:
                logger.info(f"Successfully attached file to calendar event {event_id}")
            else:
                logger.error(f"Failed to attach file to calendar event {event_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error attaching to calendar: {e}")
            return False
    
    def _find_or_create_folder(self, folder_name: str, parent_id: str = 'root') -> str:
        """Find existing folder or create new one."""
        try:
            # Search for existing folder
            existing = self.drive_service.files().list(
                q=f"name='{folder_name}' and '{parent_id}' in parents and mimeType='application/vnd.google-apps.folder'",
                fields="files(id, name)"
            ).execute()
            
            if existing.get('files'):
                return existing['files'][0]['id']
            
            # Create new folder
            folder_metadata = {
                'name': folder_name,
                'mimeType': 'application/vnd.google-apps.folder',
                'parents': [parent_id]
            }
            folder = self.drive_service.files().create(body=folder_metadata).execute()
            logger.info(f"Created new folder: {folder_name}")
            return folder.get('id')
            
        except Exception as e:
            logger.error(f"Error finding/creating folder {folder_name}: {e}")
            return parent_id  # Fallback to parent
