"""Direct test of centralized sheets integration with sample transcript."""

import os
import sys
import json
import logging
from datetime import datetime

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.services.utility.google_auth import GoogleAuthenticator
from src.tools.langchain_sheets_tool import SheetsToolLangChain

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_sample_ai_output():
    """Create sample AI output with tasks from product launch meeting."""
    return {
        "meeting_title": "Product Launch Strategy Session",
        "meeting_date": "2024-01-15",
        "attendees": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
        "summary": "Discussed Q2 product launch strategy for AI-powered analytics dashboard. Key decisions made on timeline, rollout strategy, and team responsibilities.",
        "decisions": [
            {
                "decision": "Proceed with Q2 launch timeline, targeting March 15th release date",
                "context": "Engineering confirmed development completion by March 15th with API integration testing by February 28th"
            },
            {
                "decision": "Implement phased rollout strategy starting with enterprise customers",
                "context": "Marketing prepared comprehensive go-to-market strategy with segment-specific messaging"
            },
            {
                "decision": "Sales team training in last week of February",
                "context": "Need to train team on new features and pricing model before launch"
            },
            {
                "decision": "Pricing strategy needs executive review by February 15th",
                "context": "Ensure pricing aligns with market expectations and competitive positioning"
            }
        ],
        "action_items": [
            {
                "task": "Complete API integration testing with Salesforce, HubSpot, and Slack",
                "owner": "Mike Chen",
                "deadline": "2024-02-28",
                "priority": "HIGH",
                "context": "Critical for enterprise customers who rely heavily on these integrations"
            },
            {
                "task": "Finalize all development work for production release",
                "owner": "Engineering Team",
                "deadline": "2024-03-10",
                "priority": "HIGH",
                "context": "Must be completed before March 15th launch date"
            },
            {
                "task": "Launch marketing campaign with content and PR outreach",
                "owner": "Lisa Rodriguez",
                "deadline": "2024-03-01",
                "priority": "HIGH",
                "context": "Includes blog posts, whitepapers, webinars, and social media campaigns"
            },
            {
                "task": "Prepare segment-specific messaging for customer tiers",
                "owner": "Marketing Team",
                "deadline": "2024-02-25",
                "priority": "MEDIUM",
                "context": "Different messaging for enterprise, mid-market, and SMB segments"
            },
            {
                "task": "Schedule and organize sales training sessions",
                "owner": "David Kim",
                "deadline": "2024-02-28",
                "priority": "HIGH",
                "context": "Train sales team on new features and pricing model"
            },
            {
                "task": "Prepare training materials and documentation",
                "owner": "Product Team",
                "deadline": "2024-02-25",
                "priority": "MEDIUM",
                "context": "Support materials for sales training sessions"
            },
            {
                "task": "Schedule pricing review meeting with executive team",
                "owner": "Sarah Johnson",
                "deadline": "2024-02-15",
                "priority": "HIGH",
                "context": "Final pricing strategy approval needed"
            },
            {
                "task": "Provide competitive analysis and margin calculations",
                "owner": "Finance Team",
                "deadline": "2024-02-10",
                "priority": "MEDIUM",
                "context": "Support pricing review with market data"
            },
            {
                "task": "Coordinate with DevOps for production deployment strategy",
                "owner": "Mike Chen",
                "deadline": "2024-03-05",
                "priority": "MEDIUM",
                "context": "Ensure rollback plan is ready for production deployment"
            },
            {
                "task": "Schedule early access demos with key enterprise customers",
                "owner": "David Kim",
                "deadline": "2024-02-20",
                "priority": "MEDIUM",
                "context": "Get feedback before official launch"
            }
        ],
        "next_meeting": {
            "date": "2024-02-01",
            "purpose": "Review progress on all action items"
        }
    }


def test_direct_sheets_integration():
    """Test direct integration with centralized sheets."""
    logger.info("🚀 Testing Direct Centralized Sheets Integration")
    logger.info("=" * 60)
    
    try:
        # Step 1: Initialize authentication
        logger.info("🔐 Initializing Google OAuth authentication...")
        auth = GoogleAuthenticator()
        
        if not auth.credentials or not auth.credentials.valid:
            logger.info("🔄 Starting OAuth flow...")
            success = auth.authenticate()
            if not success:
                logger.error("❌ OAuth authentication failed")
                return False
        
        # Get user email
        user_email = auth.get_user_email()
        logger.info(f"👤 Authenticated user: {user_email}")
        
        # Step 2: Initialize Sheets tool
        logger.info("📊 Initializing LangChain Sheets tool...")
        sheets_tool = SheetsToolLangChain()
        
        # Step 3: Test create_sheet action (ensure centralized sheet exists)
        logger.info("📋 Testing create_sheet action (ensure centralized meeting_task sheet)...")
        create_result = sheets_tool.create_sheet(
            user_id=user_email,
            sheet_name="meeting_task"
        )
        
        logger.info("Create sheet result:")
        logger.info(f"  Status: {create_result.status}")
        logger.info(f"  Message: {create_result.message}")
        logger.info(f"  Sheet ID: {create_result.sheet_id}")
        logger.info(f"  Sheet URL: {create_result.sheet_url}")
        
        if create_result.status != "ready":
            logger.error("❌ Failed to ensure centralized meeting_task sheet")
            return False
        
        # Step 4: Create sample AI output
        logger.info("📝 Creating sample AI output from Product Launch Strategy Session...")
        ai_output = create_sample_ai_output()
        logger.info(f"📋 Sample meeting: {ai_output['meeting_title']}")
        logger.info(f"📅 Meeting date: {ai_output['meeting_date']}")
        logger.info(f"👥 Attendees: {', '.join(ai_output['attendees'])}")
        logger.info(f"📋 Action items: {len(ai_output['action_items'])} tasks")
        
        # Step 5: Test write_tasks action
        logger.info("💾 Testing write_tasks action (write to centralized sheet)...")
        write_result = sheets_tool.write_tasks(
            user_id=user_email,
            ai_output=ai_output
        )
        
        logger.info("Write tasks result:")
        logger.info(f"  Status: {write_result.status}")
        logger.info(f"  Message: {write_result.message}")
        logger.info(f"  Tasks written: {write_result.tasks_written}")
        logger.info(f"  Sheet ID: {write_result.sheet_id}")
        logger.info(f"  Sheet URL: {write_result.sheet_url}")
        
        if write_result.status != "success":
            logger.error("❌ Failed to write tasks to centralized sheet")
            return False
        
        # Step 6: Display task details
        logger.info("\n📋 Tasks written to centralized meeting_task sheet:")
        for i, task in enumerate(ai_output['action_items'], 1):
            logger.info(f"  {i}. {task['task']}")
            logger.info(f"     Owner: {task['owner']}")
            logger.info(f"     Deadline: {task['deadline']}")
            logger.info(f"     Priority: {task['priority']}")
        
        logger.info("\n✅ Direct sheets integration test completed successfully!")
        logger.info(f"🔗 Your centralized meeting_task sheet: {write_result.sheet_url}")
        logger.info(f"📊 Total tasks written: {write_result.tasks_written}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False


def main():
    """Main test function."""
    logger.info("🎯 Direct Test: Centralized Meeting Task Sheets Integration")
    logger.info("🔍 This test directly uses the LangChain Sheets tool with sample data")
    logger.info("📝 Simulates AI output from a Product Launch Strategy Session")
    print("\n" + "=" * 60)
    
    success = test_direct_sheets_integration()
    
    print("\n" + "=" * 60)
    if success:
        logger.info("🎉 Test completed successfully!")
        logger.info("✅ The centralized meeting_task sheet integration is working correctly")
        logger.info("📊 Check your Google Sheet to see the new tasks from the product launch meeting")
        logger.info("💡 The same sheet will be used for all future meeting tasks!")
    else:
        logger.error("❌ Test failed!")
        logger.error("Please check the logs above for details")


if __name__ == "__main__":
    main()
