"""Create UserSheetMapping table for centralized meeting task sheets."""

import os
import sys
import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.constants.tables import Base, UserSheetMapping
from src.configuration.config import DB_URL

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_user_sheet_mapping_table():
    """Create the UserSheetMapping table in the database."""
    try:
        # Get database URL
        database_url = DB_URL
        if not database_url:
            logger.error("Could not get database URL")
            return False

        # Create engine
        engine = create_engine(database_url)
        
        # Create the table
        logger.info("Creating UserSheetMapping table...")
        UserSheetMapping.__table__.create(engine, checkfirst=True)
        
        logger.info("✅ UserSheetMapping table created successfully")
        
        # Verify table creation
        with engine.connect() as conn:
            result = conn.execute(text("SHOW TABLES LIKE 'user_sheet_mappings'"))
            if result.fetchone():
                logger.info("✅ Table verification successful")
                return True
            else:
                logger.error("❌ Table verification failed")
                return False
                
    except Exception as e:
        logger.error(f"❌ Error creating UserSheetMapping table: {e}")
        return False


def main():
    """Main function to create the table."""
    logger.info("🚀 Starting UserSheetMapping table creation...")
    
    success = create_user_sheet_mapping_table()
    
    if success:
        logger.info("🎉 UserSheetMapping table creation completed successfully!")
        print("\n✅ Database is ready for centralized meeting task sheets!")
        print("📋 The system will now automatically create a 'meeting_task' sheet for each user upon OAuth authentication.")
    else:
        logger.error("❌ UserSheetMapping table creation failed!")
        print("\n❌ Please check the database connection and try again.")
        sys.exit(1)


if __name__ == "__main__":
    main()
