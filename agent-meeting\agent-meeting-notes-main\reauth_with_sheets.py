"""Re-authenticate with Google APIs including Sheets scope."""

import os
import sys
import json
import logging
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Re-authenticate with Sheets scope included."""
    print("🔐 Re-authenticating with Google Sheets scope...")
    
    try:
        # Import after path setup
        from src.services.utility.google_auth import GoogleAuthenticator
        
        # Delete existing token to force re-authentication
        token_path = Path("keys/google-token.json")
        if token_path.exists():
            token_path.unlink()
            print("🗑️ Deleted existing token file")
        
        # Initialize authenticator (this will trigger OAuth flow)
        auth = GoogleAuthenticator()
        
        # Test all services including Sheets
        if auth.test_authentication():
            print("✅ Re-authentication successful with Sheets scope!")
            
            # Verify Sheets service specifically
            sheets_service = auth.get_sheets_service()
            if sheets_service:
                print("✅ Google Sheets service is ready!")
            else:
                print("❌ Google Sheets service failed to initialize")
                
        else:
            print("❌ Re-authentication failed!")
            
    except Exception as e:
        print(f"❌ Error during re-authentication: {e}")
        logger.exception("Re-authentication error")


if __name__ == "__main__":
    main()
