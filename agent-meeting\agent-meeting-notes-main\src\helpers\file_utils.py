"""File utilities for managing files and directories."""

import os
import json
import logging
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class FileManager:
    """Utility class for file and directory operations."""
    
    def __init__(self, base_path: Optional[str] = None):
        """
        Initialize FileManager.
        
        Args:
            base_path: Base directory for file operations
        """
        self.base_path = Path(base_path) if base_path else Path.cwd()
        
    def create_directory(self, path: Union[str, Path]) -> bool:
        """
        Create a directory if it doesn't exist.
        
        Args:
            path: Directory path to create
            
        Returns:
            True if successful, False otherwise
        """
        try:
            full_path = self.base_path / path if not Path(path).is_absolute() else Path(path)
            full_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {full_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to create directory {path}: {e}")
            return False
    
    def write_file(self, path: Union[str, Path], content: str, encoding: str = 'utf-8') -> bool:
        """
        Write content to a file.
        
        Args:
            path: File path
            content: Content to write
            encoding: File encoding
            
        Returns:
            True if successful, False otherwise
        """
        try:
            full_path = self.base_path / path if not Path(path).is_absolute() else Path(path)
            
            # Create parent directories if they don't exist
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(full_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            logger.info(f"Wrote file: {full_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to write file {path}: {e}")
            return False
    
    def read_file(self, path: Union[str, Path], encoding: str = 'utf-8') -> Optional[str]:
        """
        Read content from a file.
        
        Args:
            path: File path
            encoding: File encoding
            
        Returns:
            File content or None if failed
        """
        try:
            full_path = self.base_path / path if not Path(path).is_absolute() else Path(path)
            
            with open(full_path, 'r', encoding=encoding) as f:
                content = f.read()
            
            logger.info(f"Read file: {full_path}")
            return content
        except Exception as e:
            logger.error(f"Failed to read file {path}: {e}")
            return None
    
    def write_json(self, path: Union[str, Path], data: Dict[str, Any], indent: int = 2) -> bool:
        """
        Write data to a JSON file.
        
        Args:
            path: File path
            data: Data to write
            indent: JSON indentation
            
        Returns:
            True if successful, False otherwise
        """
        try:
            content = json.dumps(data, indent=indent, ensure_ascii=False)
            return self.write_file(path, content)
        except Exception as e:
            logger.error(f"Failed to write JSON file {path}: {e}")
            return False
    
    def read_json(self, path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """
        Read data from a JSON file.
        
        Args:
            path: File path
            
        Returns:
            Parsed JSON data or None if failed
        """
        try:
            content = self.read_file(path)
            if content:
                return json.loads(content)
            return None
        except Exception as e:
            logger.error(f"Failed to read JSON file {path}: {e}")
            return None
    
    def file_exists(self, path: Union[str, Path]) -> bool:
        """
        Check if a file exists.
        
        Args:
            path: File path
            
        Returns:
            True if file exists, False otherwise
        """
        full_path = self.base_path / path if not Path(path).is_absolute() else Path(path)
        return full_path.exists() and full_path.is_file()
    
    def directory_exists(self, path: Union[str, Path]) -> bool:
        """
        Check if a directory exists.
        
        Args:
            path: Directory path
            
        Returns:
            True if directory exists, False otherwise
        """
        full_path = self.base_path / path if not Path(path).is_absolute() else Path(path)
        return full_path.exists() and full_path.is_dir()
    
    def delete_file(self, path: Union[str, Path]) -> bool:
        """
        Delete a file.
        
        Args:
            path: File path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            full_path = self.base_path / path if not Path(path).is_absolute() else Path(path)
            
            if full_path.exists():
                full_path.unlink()
                logger.info(f"Deleted file: {full_path}")
                return True
            else:
                logger.warning(f"File does not exist: {full_path}")
                return False
        except Exception as e:
            logger.error(f"Failed to delete file {path}: {e}")
            return False
    
    def delete_directory(self, path: Union[str, Path]) -> bool:
        """
        Delete a directory and its contents.
        
        Args:
            path: Directory path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            full_path = self.base_path / path if not Path(path).is_absolute() else Path(path)
            
            if full_path.exists():
                shutil.rmtree(full_path)
                logger.info(f"Deleted directory: {full_path}")
                return True
            else:
                logger.warning(f"Directory does not exist: {full_path}")
                return False
        except Exception as e:
            logger.error(f"Failed to delete directory {path}: {e}")
            return False
    
    def list_files(self, path: Union[str, Path] = ".", pattern: str = "*") -> List[str]:
        """
        List files in a directory.
        
        Args:
            path: Directory path
            pattern: File pattern (glob)
            
        Returns:
            List of file paths
        """
        try:
            full_path = self.base_path / path if not Path(path).is_absolute() else Path(path)
            
            if full_path.is_dir():
                files = [str(f.relative_to(self.base_path)) for f in full_path.glob(pattern) if f.is_file()]
                return sorted(files)
            else:
                logger.warning(f"Path is not a directory: {full_path}")
                return []
        except Exception as e:
            logger.error(f"Failed to list files in {path}: {e}")
            return []
    
    def get_file_info(self, path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """
        Get file information.
        
        Args:
            path: File path
            
        Returns:
            File information dictionary or None if failed
        """
        try:
            full_path = self.base_path / path if not Path(path).is_absolute() else Path(path)
            
            if full_path.exists():
                stat = full_path.stat()
                return {
                    "path": str(full_path),
                    "name": full_path.name,
                    "size": stat.st_size,
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    "is_file": full_path.is_file(),
                    "is_directory": full_path.is_dir()
                }
            else:
                return None
        except Exception as e:
            logger.error(f"Failed to get file info for {path}: {e}")
            return None
    
    def copy_file(self, src: Union[str, Path], dst: Union[str, Path]) -> bool:
        """
        Copy a file.
        
        Args:
            src: Source file path
            dst: Destination file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            src_path = self.base_path / src if not Path(src).is_absolute() else Path(src)
            dst_path = self.base_path / dst if not Path(dst).is_absolute() else Path(dst)
            
            # Create parent directories if they don't exist
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(src_path, dst_path)
            logger.info(f"Copied file from {src_path} to {dst_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to copy file from {src} to {dst}: {e}")
            return False
    
    def move_file(self, src: Union[str, Path], dst: Union[str, Path]) -> bool:
        """
        Move a file.
        
        Args:
            src: Source file path
            dst: Destination file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            src_path = self.base_path / src if not Path(src).is_absolute() else Path(src)
            dst_path = self.base_path / dst if not Path(dst).is_absolute() else Path(dst)
            
            # Create parent directories if they don't exist
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(str(src_path), str(dst_path))
            logger.info(f"Moved file from {src_path} to {dst_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to move file from {src} to {dst}: {e}")
            return False
