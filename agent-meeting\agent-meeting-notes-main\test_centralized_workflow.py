"""Test the complete 7-step workflow with centralized meeting_task sheet."""

import os
import sys
import json
import logging
from datetime import datetime, timed<PERSON><PERSON>

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.services.utility.google_auth import GoogleAuthenticator
from src.agents.langchain_meeting_agent import LangChainMeetingAgent

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_sample_transcript_file():
    """Create a sample meeting transcript file for testing."""
    transcript_content = """
Meeting: Product Launch Strategy Session
Date: January 15, 2024
Time: 2:00 PM - 3:30 PM
Attendees: <PERSON> (Product Manager), <PERSON> (Engineering Lead), <PERSON> (Marketing Director), <PERSON> (Sales Manager)

[2:00 PM] <PERSON>: Good afternoon everyone. Thank you for joining today's product launch strategy session. We're here to finalize our approach for the Q2 product launch of our new AI-powered analytics dashboard.

[2:02 PM] <PERSON>: Thanks <PERSON>. From the engineering perspective, we're on track to complete development by March 15th. However, we need to address the API integration testing with our third-party partners.

[2:04 PM] <PERSON>: That's great to hear <PERSON>. From marketing, we've prepared a comprehensive go-to-market strategy. We're planning a phased rollout starting with our enterprise customers, followed by mid-market, and then SMB segments.

[2:06 PM] David Kim: The sales team is excited about this launch. We've already received positive feedback from our beta customers. However, we need to ensure our pricing strategy aligns with market expectations.

[2:08 PM] <PERSON> Johnson: Excellent points everyone. Let's dive into the specific action items. Mike, can you elaborate on the API integration timeline?

[2:10 PM] Mike Chen: Absolutely. We need to complete integration testing with Salesforce, HubSpot, and Slack APIs by February 28th. This is critical for our enterprise customers who rely heavily on these integrations.

[2:12 PM] Sarah Johnson: That sounds reasonable. Lisa, what's our content marketing timeline?

[2:14 PM] Lisa Rodriguez: We're planning to launch our content campaign on March 1st. This includes blog posts, whitepapers, webinars, and social media campaigns. We also need to coordinate with the PR team for media outreach.

[2:16 PM] David Kim: From a sales perspective, we need to train our team on the new features and pricing model. I propose we conduct training sessions in the last week of February.

[2:18 PM] Sarah Johnson: Great suggestions. Let me summarize our key decisions and action items:

Decision 1: We will proceed with the Q2 launch timeline, targeting a March 15th release date.
Action: Mike to complete API integration testing by February 28th.
Action: Engineering team to finalize all development work by March 10th.

Decision 2: We will implement a phased rollout strategy starting with enterprise customers.
Action: Lisa to coordinate marketing campaign launch for March 1st.
Action: Marketing team to prepare segment-specific messaging for each customer tier.

Decision 3: Sales team training will be conducted in the last week of February.
Action: David to schedule and organize sales training sessions.
Action: Product team to prepare training materials and documentation.

Decision 4: Pricing strategy needs final review and approval.
Action: Sarah to schedule pricing review meeting with executive team by February 15th.
Action: Finance team to provide competitive analysis and margin calculations.

[2:25 PM] Mike Chen: I'll also need to coordinate with the DevOps team for the production deployment strategy. We should have a rollback plan ready.

[2:27 PM] Lisa Rodriguez: I'll work with the design team to finalize all marketing assets and ensure brand consistency across all channels.

[2:29 PM] David Kim: I'll reach out to our key enterprise customers to schedule early access demos before the official launch.

[2:31 PM] Sarah Johnson: Perfect. Let's also establish our success metrics. We're targeting 500 new customers in the first quarter post-launch and $2M in additional ARR.

[2:33 PM] Mike Chen: From a technical perspective, we need to ensure our infrastructure can handle the expected load. I'll coordinate with the infrastructure team.

[2:35 PM] Lisa Rodriguez: We should also plan for customer feedback collection and rapid iteration based on initial user responses.

[2:37 PM] David Kim: I agree. We need a clear process for handling customer inquiries and feature requests during the launch period.

[2:39 PM] Sarah Johnson: Excellent discussion everyone. Let's schedule a follow-up meeting for February 1st to review progress on all action items. Thank you all for your time and dedication to making this launch successful.

[2:40 PM] Meeting ended.

Additional Notes:
- Budget approved for additional marketing spend of $150K for launch campaign
- Legal team to review all customer-facing documentation by February 20th
- Customer success team to prepare onboarding materials for new feature set
- Engineering to provide detailed technical documentation for sales team
"""

    # Create transcript file
    transcript_path = "sample_product_launch_transcript.txt"
    with open(transcript_path, 'w', encoding='utf-8') as f:
        f.write(transcript_content)
    
    logger.info(f"Created sample transcript: {transcript_path}")
    return transcript_path


async def test_complete_workflow():
    """Test the complete 7-step workflow with centralized sheets."""
    logger.info("🚀 Starting Complete Workflow Test with Centralized Sheets")
    logger.info("=" * 70)
    
    try:
        # Step 1: Create sample transcript
        transcript_path = create_sample_transcript_file()
        
        # Step 2: Initialize authentication
        logger.info("🔐 Initializing Google OAuth authentication...")
        auth = GoogleAuthenticator()
        
        if not auth.credentials or not auth.credentials.valid:
            logger.info("🔄 Starting OAuth flow...")
            success = auth.authenticate()
            if not success:
                logger.error("❌ OAuth authentication failed")
                return False
        
        # Get user email
        user_email = auth.get_user_email()
        logger.info(f"👤 Authenticated user: {user_email}")
        
        # Step 3: Initialize the meeting agent
        logger.info("🤖 Initializing LangChain Meeting Agent...")
        agent = LangChainMeetingAgent()
        
        # Step 4: Execute the 7-step workflow
        logger.info("🔄 Executing 7-step meeting workflow...")
        logger.info("📋 This will test the centralized meeting_task sheet approach")
        
        # Execute workflow with 30-minute time window
        result = await agent.execute_7_step_workflow(time_window_minutes=30)
        
        # Step 5: Analyze results
        logger.info("📊 Analyzing workflow results...")
        
        if result.get('success'):
            logger.info("✅ Workflow completed successfully!")
            
            # Check if sheets step was executed
            steps = result.get('steps', [])
            sheets_step = None
            for step in steps:
                if 'sheets' in step.get('step_name', '').lower() or step.get('step_number') == 7:
                    sheets_step = step
                    break
            
            if sheets_step:
                logger.info("📊 Sheets step details:")
                logger.info(f"   Status: {sheets_step.get('status')}")
                logger.info(f"   Message: {sheets_step.get('message')}")
                
                if sheets_step.get('status') == 'completed':
                    logger.info("✅ Tasks successfully written to centralized meeting_task sheet!")
                else:
                    logger.warning("⚠️ Sheets step may not have completed successfully")
            
            # Display summary
            logger.info("\n📋 Workflow Summary:")
            for step in steps:
                status_emoji = "✅" if step.get('status') == 'completed' else "❌" if step.get('status') == 'failed' else "⏳"
                logger.info(f"   {status_emoji} Step {step.get('step_number')}: {step.get('step_name')}")
            
            return True
        else:
            logger.error("❌ Workflow failed!")
            logger.error(f"Error: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False
    
    finally:
        # Cleanup
        if os.path.exists(transcript_path):
            os.remove(transcript_path)
            logger.info(f"🧹 Cleaned up transcript file: {transcript_path}")


async def main():
    """Main test function."""
    logger.info("🎯 Testing Centralized Meeting Task Sheets with Real Workflow")
    logger.info("🔍 This test will verify that the agent uses the existing meeting_task sheet")
    logger.info("📝 A new sample transcript will be processed through the complete 7-step workflow")
    print("\n" + "=" * 70)
    
    success = await test_complete_workflow()
    
    print("\n" + "=" * 70)
    if success:
        logger.info("🎉 Test completed successfully!")
        logger.info("✅ The centralized meeting_task sheet approach is working correctly")
        logger.info("📊 Check your Google Sheet to see the new tasks from the product launch meeting")
        print(f"\n🔗 Your centralized sheet: https://docs.google.com/spreadsheets/d/1HJwwMD9ZceiuA4H4YnQvMbVwwpmEVvmf5UHrt_3bSco")
    else:
        logger.error("❌ Test failed!")
        logger.error("Please check the logs above for details")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
