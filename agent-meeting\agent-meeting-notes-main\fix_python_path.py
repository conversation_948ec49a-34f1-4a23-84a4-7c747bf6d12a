#!/usr/bin/env python3
"""
Fix Python Path Issues - Permanent Solution
This script fixes the Python path issue where the system looks for Python 3.11 instead of 3.12
"""

import os
import sys
import subprocess
import winreg
from pathlib import Path

def get_current_python_path():
    """Get the current Python executable path."""
    return sys.executable

def fix_python_path():
    """Fix Python path issues permanently."""
    print("🔧 Fixing Python Path Issues...")
    
    # Get current Python path
    current_python = get_current_python_path()
    python_dir = str(Path(current_python).parent)
    scripts_dir = str(Path(current_python).parent / "Scripts")
    
    print(f"✅ Current Python: {current_python}")
    print(f"✅ Python Directory: {python_dir}")
    print(f"✅ Scripts Directory: {scripts_dir}")
    
    # Check if paths are in system PATH
    current_path = os.environ.get('PATH', '')
    
    paths_to_add = []
    if python_dir not in current_path:
        paths_to_add.append(python_dir)
    if scripts_dir not in current_path:
        paths_to_add.append(scripts_dir)
    
    if paths_to_add:
        print(f"🔄 Adding to PATH: {paths_to_add}")
        
        # Update current session PATH
        new_path = os.pathsep.join(paths_to_add + [current_path])
        os.environ['PATH'] = new_path
        
        print("✅ PATH updated for current session")
        print("💡 For permanent fix, add these paths to your system PATH:")
        for path in paths_to_add:
            print(f"   - {path}")
    else:
        print("✅ Python paths already in PATH")
    
    return current_python

def test_python_commands():
    """Test that Python commands work correctly."""
    print("\n🧪 Testing Python Commands...")
    
    try:
        # Test python command
        result = subprocess.run([sys.executable, "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Python version: {result.stdout.strip()}")
        else:
            print(f"❌ Python command failed: {result.stderr}")
            
        # Test pip command
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Pip version: {result.stdout.strip()}")
        else:
            print(f"❌ Pip command failed: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Error testing commands: {e}")

def create_batch_files():
    """Create batch files for easy Python access."""
    print("\n📝 Creating helper batch files...")
    
    python_exe = sys.executable
    
    # Create python.bat
    batch_content = f'@echo off\n"{python_exe}" %*\n'
    with open("python.bat", "w") as f:
        f.write(batch_content)
    print("✅ Created python.bat")
    
    # Create pip.bat
    pip_content = f'@echo off\n"{python_exe}" -m pip %*\n'
    with open("pip.bat", "w") as f:
        f.write(pip_content)
    print("✅ Created pip.bat")
    
    print("💡 You can now use ./python.bat and ./pip.bat if needed")

if __name__ == "__main__":
    print("🚀 Python Path Fix Tool")
    print("=" * 50)
    
    # Fix Python path
    python_path = fix_python_path()
    
    # Test commands
    test_python_commands()
    
    # Create batch files as backup
    create_batch_files()
    
    print("\n" + "=" * 50)
    print("✅ Python path fix completed!")
    print(f"🐍 Using Python: {python_path}")
    print("\n💡 If you still have issues, use the full path:")
    print(f'   "{python_path}" your_script.py')
