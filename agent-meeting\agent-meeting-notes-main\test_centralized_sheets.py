"""Test script for centralized meeting_task sheets functionality."""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, Any

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.services.utility.google_auth import GoogleAuthenticator
from src.services.user_sheet_manager import UserSheetManager
from src.services.google_sheets_service import GoogleSheetsService
from src.tools.langchain_sheets_tool import SheetsToolLangChain

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_sample_ai_output() -> Dict[str, Any]:
    """Create sample AI output for testing."""
    return {
        "meeting_title": "Centralized Sheets Test Meeting",
        "meeting_date": datetime.now().strftime("%Y-%m-%d"),
        "attendees": ["<EMAIL>", "<EMAIL>"],
        "outcomes": [
            {
                "decision": "Implement centralized task management",
                "context": "All meeting tasks should be logged to a single user sheet",
                "owner": "<EMAIL>",
                "actions": [
                    {
                        "task": "Set up centralized meeting_task sheet for user",
                        "priority": "HIGH",
                        "owner": "<EMAIL>",
                        "deadline": "2024-01-15"
                    },
                    {
                        "task": "Test automatic sheet creation upon OAuth",
                        "priority": "MEDIUM",
                        "owner": "<EMAIL>",
                        "deadline": "2024-01-16"
                    }
                ]
            },
            {
                "decision": "Validate task extraction workflow",
                "context": "Ensure all tasks are properly formatted and stored",
                "owner": "<EMAIL>",
                "actions": [
                    {
                        "task": "Review task data structure in sheets",
                        "priority": "MEDIUM",
                        "owner": "<EMAIL>",
                        "deadline": "2024-01-17"
                    }
                ]
            }
        ]
    }


def test_authentication():
    """Test Google OAuth authentication with Sheets scope."""
    logger.info("🔐 Testing Google OAuth authentication...")
    
    try:
        auth = GoogleAuthenticator()
        
        # Check if we need to authenticate
        if not auth.credentials or not auth.credentials.valid:
            logger.info("🔄 Starting OAuth flow...")
            success = auth.authenticate()
            if not success:
                logger.error("❌ OAuth authentication failed")
                return None
        
        # Test authentication
        if auth.test_authentication():
            logger.info("✅ OAuth authentication successful")
            
            # Get user email
            user_email = auth.get_user_email()
            if user_email:
                logger.info(f"👤 Authenticated user: {user_email}")
                return auth
            else:
                logger.error("❌ Could not get user email")
                return None
        else:
            logger.error("❌ Authentication test failed")
            return None
            
    except Exception as e:
        logger.error(f"❌ Authentication error: {e}")
        return None


def test_user_sheet_manager(auth: GoogleAuthenticator):
    """Test UserSheetManager functionality."""
    logger.info("📊 Testing UserSheetManager...")
    
    try:
        manager = UserSheetManager(auth)
        
        # Test connection
        if not manager.test_connection():
            logger.error("❌ UserSheetManager connection test failed")
            return None
        
        logger.info("✅ UserSheetManager connection successful")
        
        # Get user email
        user_email = auth.get_user_email()
        if not user_email:
            logger.error("❌ Could not get user email")
            return None
        
        # Test sheet creation
        logger.info(f"📋 Creating/ensuring meeting_task sheet for {user_email}...")
        sheet_id = manager.create_meeting_task_sheet_if_not_exists(user_email)
        
        if sheet_id:
            logger.info(f"✅ Meeting task sheet ready: {sheet_id}")
            logger.info(f"🔗 Sheet URL: https://docs.google.com/spreadsheets/d/{sheet_id}")
            return manager, sheet_id
        else:
            logger.error("❌ Failed to create/ensure meeting_task sheet")
            return None
            
    except Exception as e:
        logger.error(f"❌ UserSheetManager test error: {e}")
        return None


def test_task_extraction_and_writing(manager: UserSheetManager, sheet_id: str):
    """Test task extraction and writing to centralized sheet."""
    logger.info("📝 Testing task extraction and writing...")
    
    try:
        # Create sample AI output
        ai_output = create_sample_ai_output()
        logger.info(f"📄 Created sample AI output with meeting: {ai_output['meeting_title']}")
        
        # Extract tasks
        tasks = manager.extract_tasks_from_summary(ai_output)
        logger.info(f"📋 Extracted {len(tasks)} tasks from AI output")
        
        for i, task in enumerate(tasks, 1):
            logger.info(f"  Task {i}: {task['description']} (Priority: {task['priority']}, Owner: {task['assigned_to']})")
        
        # Write tasks to sheet
        logger.info(f"💾 Writing tasks to sheet {sheet_id}...")
        success = manager.append_tasks_to_sheet(sheet_id, tasks)
        
        if success:
            logger.info(f"✅ Successfully wrote {len(tasks)} tasks to centralized meeting_task sheet")
            return True
        else:
            logger.error("❌ Failed to write tasks to sheet")
            return False
            
    except Exception as e:
        logger.error(f"❌ Task extraction/writing test error: {e}")
        return False


def test_google_sheets_service(auth: GoogleAuthenticator):
    """Test GoogleSheetsService with centralized approach."""
    logger.info("🔧 Testing GoogleSheetsService...")
    
    try:
        service = GoogleSheetsService(auth)
        
        # Create sample AI output
        ai_output = create_sample_ai_output()
        
        # Test centralized workflow
        logger.info("🔄 Testing centralized task writing workflow...")
        success = service.write_tasks_from_ai_summary(ai_output)
        
        if success:
            logger.info("✅ GoogleSheetsService centralized workflow successful")
            
            # Get sheet info
            sheet_info = service.get_user_sheet_info()
            if sheet_info:
                logger.info(f"📊 Sheet Info: {sheet_info}")
                return True
            else:
                logger.warning("⚠️ Could not get sheet info")
                return True  # Still consider success
        else:
            logger.error("❌ GoogleSheetsService centralized workflow failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ GoogleSheetsService test error: {e}")
        return False


def test_langchain_tool(auth: GoogleAuthenticator):
    """Test LangChain Sheets tool with centralized approach."""
    logger.info("🛠️ Testing LangChain Sheets tool...")
    
    try:
        tool = SheetsToolLangChain(auth=auth)
        
        # Get user email
        user_email = auth.get_user_email()
        if not user_email:
            logger.error("❌ Could not get user email for LangChain tool test")
            return False
        
        # Test create_sheet action
        logger.info("📋 Testing create_sheet action...")
        create_result = tool._run(
            action="create_sheet",
            user_id=user_email
        )
        
        logger.info(f"Create sheet result: {create_result}")
        
        # Test write_tasks action
        logger.info("📝 Testing write_tasks action...")
        ai_output = create_sample_ai_output()
        
        write_result = tool._run(
            action="write_tasks",
            user_id=user_email,
            ai_output=ai_output
        )
        
        logger.info(f"Write tasks result: {write_result}")
        
        # Parse results
        create_data = json.loads(create_result)
        write_data = json.loads(write_result)
        
        if create_data.get("status") in ["ready", "created"] and write_data.get("status") == "success":
            logger.info("✅ LangChain Sheets tool test successful")
            return True
        else:
            logger.error("❌ LangChain Sheets tool test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ LangChain tool test error: {e}")
        return False


def main():
    """Main test function."""
    logger.info("🚀 Starting Centralized Meeting Task Sheets Test")
    logger.info("=" * 60)
    
    # Test 1: Authentication
    auth = test_authentication()
    if not auth:
        logger.error("❌ Authentication failed - stopping tests")
        return
    
    print("\n" + "=" * 60)
    
    # Test 2: UserSheetManager
    manager_result = test_user_sheet_manager(auth)
    if not manager_result:
        logger.error("❌ UserSheetManager test failed - stopping tests")
        return
    
    manager, sheet_id = manager_result
    print("\n" + "=" * 60)
    
    # Test 3: Task extraction and writing
    if not test_task_extraction_and_writing(manager, sheet_id):
        logger.error("❌ Task extraction/writing test failed")
        return
    
    print("\n" + "=" * 60)
    
    # Test 4: GoogleSheetsService
    if not test_google_sheets_service(auth):
        logger.error("❌ GoogleSheetsService test failed")
        return
    
    print("\n" + "=" * 60)
    
    # Test 5: LangChain tool
    if not test_langchain_tool(auth):
        logger.error("❌ LangChain tool test failed")
        return
    
    print("\n" + "=" * 60)
    logger.info("🎉 All centralized sheets tests completed successfully!")
    logger.info("✅ The system is ready for centralized meeting task management")
    
    # Get final sheet info
    user_email = auth.get_user_email()
    if user_email:
        print(f"\n📊 Your centralized meeting_task sheet is ready!")
        print(f"👤 User: {user_email}")
        print(f"📋 Sheet ID: {sheet_id}")
        print(f"🔗 Sheet URL: https://docs.google.com/spreadsheets/d/{sheet_id}")
        print(f"\n💡 All future meeting tasks will be automatically logged to this sheet!")


if __name__ == "__main__":
    main()
