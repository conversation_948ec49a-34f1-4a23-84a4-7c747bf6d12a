#!/usr/bin/env python3
"""
Authentication setup and testing script for Meeting Intelligence Agent.
This script will properly configure Google OAuth2 authentication.
"""

import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_credential_files():
    """Check if all required credential files exist."""
    print("🔍 Checking credential files...")
    
    required_files = {
        'OAuth Credentials': './keys/google-oauth-credentials.json',
        'Token File': './keys/google-token.json',
        'Gmail Credentials': './keys/gmail-credentials.json',
        'Service Account': './keys/google-service-account.json'
    }
    
    status = {}
    for name, path in required_files.items():
        exists = os.path.exists(path)
        status[name] = exists
        print(f"  {'✅' if exists else '❌'} {name}: {path}")
        
        if exists:
            try:
                with open(path, 'r') as f:
                    data = json.load(f)
                    if name == 'Token File' and 'expiry' in data:
                        expiry = datetime.fromisoformat(data['expiry'].replace('Z', '+00:00'))
                        is_expired = expiry < datetime.now(expiry.tzinfo)
                        print(f"    Token expires: {data['expiry']} {'(EXPIRED)' if is_expired else '(VALID)'}")
            except Exception as e:
                print(f"    ⚠️  Error reading file: {e}")
    
    return status

def test_google_auth():
    """Test Google authentication."""
    print("\n🔐 Testing Google Authentication...")
    
    try:
        from src.services.utility.google_auth import GoogleAuthenticator
        
        # Initialize authenticator
        auth = GoogleAuthenticator()
        
        if not auth.credentials:
            print("❌ No valid credentials found")
            return False
        
        print("✅ GoogleAuthenticator initialized")
        
        # Test if credentials are valid
        if auth.credentials.valid:
            print("✅ Credentials are valid")
        elif auth.credentials.expired and auth.credentials.refresh_token:
            print("⚠️  Credentials expired, attempting refresh...")
            try:
                from google.auth.transport.requests import Request
                auth.credentials.refresh(Request())
                print("✅ Credentials refreshed successfully")
                
                # Save refreshed token
                auth._save_credentials()
                print("✅ Refreshed token saved")
                
            except Exception as e:
                print(f"❌ Failed to refresh credentials: {e}")
                return False
        else:
            print("❌ Credentials are invalid and cannot be refreshed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return False

def test_google_services():
    """Test Google service connections."""
    print("\n🔧 Testing Google Services...")
    
    try:
        from src.services.utility.google_auth import GoogleAuthenticator
        from src.services.utility.calendar_service import GoogleCalendarService
        from src.services.email_service import EmailService
        
        # Initialize authenticator
        auth = GoogleAuthenticator()
        
        if not auth.credentials or not auth.credentials.valid:
            print("❌ Invalid credentials, cannot test services")
            return False
        
        # Test Calendar Service
        try:
            calendar_service = GoogleCalendarService(auth)
            print("✅ Calendar Service initialized")
            
            # Test getting calendars
            calendars = calendar_service.get_calendars()
            print(f"✅ Found {len(calendars)} calendars")
            
        except Exception as e:
            print(f"❌ Calendar Service failed: {e}")
        
        # Test Email Service
        try:
            email_service = EmailService(provider="gmail", google_auth=auth)
            print("✅ Email Service initialized")
            
        except Exception as e:
            print(f"❌ Email Service failed: {e}")
        
        # Test Drive Service
        try:
            from googleapiclient.discovery import build
            drive_service = build('drive', 'v3', credentials=auth.credentials)
            
            # Test listing files
            results = drive_service.files().list(pageSize=1).execute()
            print("✅ Drive Service working")
            
        except Exception as e:
            print(f"❌ Drive Service failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Service test failed: {e}")
        return False

def refresh_authentication():
    """Force refresh authentication."""
    print("\n🔄 Refreshing Authentication...")
    
    try:
        from src.services.utility.google_auth import GoogleAuthenticator
        
        # Delete existing token to force re-authentication
        token_path = './keys/google-token.json'
        if os.path.exists(token_path):
            backup_path = f"{token_path}.backup"
            os.rename(token_path, backup_path)
            print(f"✅ Backed up existing token to {backup_path}")
        
        # Initialize new authenticator (will trigger OAuth flow)
        print("🌐 Starting OAuth flow...")
        print("📱 A browser window will open for authentication")
        
        auth = GoogleAuthenticator()
        
        if auth.credentials and auth.credentials.valid:
            print("✅ New authentication successful!")
            return True
        else:
            print("❌ Authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ Refresh failed: {e}")
        return False

def test_agent_with_auth():
    """Test the agent with proper authentication."""
    print("\n🤖 Testing Agent with Authentication...")
    
    try:
        from src.agents.langchain_meeting_agent import LangChainMeetingAgent
        
        # Initialize agent
        agent = LangChainMeetingAgent()
        print("✅ Agent initialized")
        
        # Check tools
        if hasattr(agent, 'tools') and agent.tools:
            print(f"✅ {len(agent.tools)} tools loaded:")
            for tool in agent.tools:
                print(f"   - {tool.name}")
        
        # Test a simple chat
        try:
            response = agent.chat("What Google services can you access?")
            print(f"✅ Chat response: {response[:100]}...")
        except Exception as e:
            print(f"⚠️  Chat test failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent test failed: {e}")
        return False

def main():
    """Main authentication setup and testing."""
    print("🚀 Meeting Intelligence Agent - Authentication Setup")
    print("=" * 60)
    
    # Step 1: Check files
    file_status = check_credential_files()
    
    # Step 2: Test authentication
    auth_success = test_google_auth()
    
    if not auth_success:
        print("\n⚠️  Authentication failed. Would you like to refresh? (y/n)")
        response = input().lower().strip()
        if response == 'y':
            auth_success = refresh_authentication()
    
    if auth_success:
        # Step 3: Test services
        service_success = test_google_services()
        
        # Step 4: Test agent
        agent_success = test_agent_with_auth()
        
        # Summary
        print("\n📊 Authentication Setup Summary")
        print("=" * 40)
        print(f"Authentication: {'✅ SUCCESS' if auth_success else '❌ FAILED'}")
        print(f"Google Services: {'✅ SUCCESS' if service_success else '❌ FAILED'}")
        print(f"Agent Integration: {'✅ SUCCESS' if agent_success else '❌ FAILED'}")
        
        if auth_success and service_success and agent_success:
            print("\n🎉 AUTHENTICATION SETUP COMPLETE!")
            print("Your agent is now fully authenticated and ready to use.")
            return True
        else:
            print("\n⚠️  Some issues remain. Check the output above.")
            return False
    else:
        print("\n❌ Authentication setup failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
