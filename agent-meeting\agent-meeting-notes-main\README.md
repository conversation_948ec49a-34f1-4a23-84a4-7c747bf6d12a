# Meeting Intelligence Agent

[![Python](https://img.shields.io/badge/Python-3.12%2B-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104.0-green.svg)](https://fastapi.tiangolo.com)
[![<PERSON><PERSON>hain](https://img.shields.io/badge/LangChain-0.2.0-orange.svg)](https://langchain.com)
[![Google Cloud](https://img.shields.io/badge/Google%20Cloud-Vertex%20AI-red.svg)](https://cloud.google.com/vertex-ai)

An enterprise-grade AI system designed for end-to-end meeting intelligence. Built with FastAPI, LangChain, and Google Cloud Vertex AI, it delivers fully automated processing, summarization, distribution, and archiving of meeting content with Google Sheets task extraction.

## 🎯 Overview

The Meeting Intelligence Agent executes a comprehensive **7-step post-meeting workflow**:

1. **🔍 Identify Meeting & Transcript** - Detect recent meetings and fetch transcripts
2. **🤖 AI Summarization** - Generate intelligent summaries using Gemini Pro
3. **📧 Email Distribution** - Send professional HTML summaries to attendees
4. **💾 Google Drive Storage** - Organize and store meeting files
5. **📅 Calendar Integration** - Attach summaries to calendar events
6. **📁 File Management** - Clean up and organize temporary files
7. **📊 Google Sheets Tasks** - Extract and log action items to centralized sheets

## ✨ Key Features

- **🔄 Complete 7-step post-meeting pipeline** with Google Sheets integration
- **🤖 Intelligent LangChain orchestration** with custom tools
- **🧠 Google Cloud Vertex AI (Gemini Pro)** for advanced summarization
- **📧 Professional HTML email templates** with automatic distribution
- **💾 Automated Google Drive** file management and organization
- **📅 Calendar event integration** with attachment support
- **📊 Google Sheets task extraction** with centralized logging
- **🔐 Secure Google OAuth2 authentication** for all services
- **⏰ Configurable scheduling** with 30-minute recurring execution
- **🚫 Duplicate prevention** - processes each meeting only once
- **🌐 FastAPI REST endpoints** for monitoring and control
- **💬 Interactive LangChain agent** chat interface
- **🗄️ MySQL database integration** for persistent storage

## System Architecture

### Technology Stack

- **Framework**: FastAPI 0.104.0
- **AI Orchestration**: LangChain 0.2.0 + ChatVertexAI
- **AI Provider**: Google Cloud Vertex AI (Gemini 2.0 Flash)
- **Database**: MySQL 8.0
- **Authentication**: Google OAuth 2.0
- **Notifications**: Gmail API or SendGrid
- **Scheduling**: Python-Schedule and Cron

### Project Structure

```
src/
├── api/                       # FastAPI REST API
│   ├── main.py
│   └── routers/
├── agents/
│   └── langchain_meeting_agent.py
├── tools/                    # LangChain tools
├── services/                 # Business logic
├── utility/                  # Shared helpers
├── configuration/
├── constants/
```

## 🚀 Getting Started

### 📋 Prerequisites

- **Python 3.12+** (recommended)
- **Google Cloud Project** with Vertex AI API enabled
- **Google Workspace account** with admin access
- **MySQL database** (local or cloud)
- **Gmail account** for email notifications

### 📦 Installation

```bash
# Clone the repository
git clone <repository-url>
cd agent-meeting-notes-main

# Create virtual environment
python -m venv venvagent
source venvagent/bin/activate  # On Windows: venvagent\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Create required directories
mkdir -p keys output logging
```

### 🔧 Environment Configuration

Create a `.env` file in the project root:

```bash
# Google Cloud Configuration
GOOGLE_PROJECT_ID=your-google-cloud-project-id
GOOGLE_APPLICATION_CREDENTIALS=./keys/google-service-account.json
GMAIL_CREDENTIALS_PATH=./keys/gmail-credentials.json
GEMINI_API_KEY=your-gemini-api-key
GMAIL_FROM_EMAIL=<EMAIL>
GMAIL_TOKEN_PATH=./keys/google-token.json

# Vertex AI Configuration
VERTEX_AI_LOCATION=us-central1
VERTEX_AI_MODEL=gemini-pro

# Email Provider Configuration
EMAIL_PROVIDER=gmail

# FastAPI Server Configuration
HOST=0.0.0.0
PORT=8000

# MySQL Database Configuration
MYSQL_HOST=your-mysql-host
MYSQL_PORT=3306
MYSQL_USERNAME=your-db-user
MYSQL_PASSWORD=your-db-password
MYSQL_DATABASE=meeting-intelligence

# Agent Configuration
OUTPUT_DIR=./output
ENABLE_DEDUPLICATION=true
MAX_RETRIES=3
DEBUG_MODE=false

# SSL Certificate Configuration (Windows)
SSL_CERT_FILE=path-to-your-python-certifi-cacert.pem
REQUESTS_CA_BUNDLE=path-to-your-python-certifi-cacert.pem
CURL_CA_BUNDLE=path-to-your-python-certifi-cacert.pem
```

### 🔐 Authentication Setup

#### Step 1: Google Cloud Setup

1. **Create Google Cloud Project**:

   ```bash
   # Enable required APIs
   gcloud services enable calendar-json.googleapis.com
   gcloud services enable drive.googleapis.com
   gcloud services enable gmail.googleapis.com
   gcloud services enable sheets.googleapis.com
   gcloud services enable aiplatform.googleapis.com
   ```

2. **Create Service Account** (for server-to-server operations):
   ```bash
   gcloud iam service-accounts create meeting-intelligence-agent
   gcloud iam service-accounts keys create ./keys/google-service-account.json \
     --iam-account=<EMAIL>
   ```

#### Step 2: OAuth2 Setup (for user authentication)

1. **Create OAuth2 Credentials**:

   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Navigate to APIs & Services > Credentials
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Application type: "Desktop application"
   - Download the JSON file as `./keys/gmail-credentials.json`

2. **Run Authentication Setup**:
   ```bash
   # This will open a browser for OAuth consent
   python setup_auth.py
   ```

#### Step 3: Database Setup

```bash
# Create MySQL database
mysql -u root -p
CREATE DATABASE meeting_intelligence;
CREATE USER 'meeting_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON meeting_intelligence.* TO 'meeting_user'@'localhost';
FLUSH PRIVILEGES;
```

## 🏃‍♂️ Running the System

### 🎯 Quick Start (Recommended)

1. **Complete Authentication** (first time only):

   ```bash
   # Run OAuth authentication setup
   python setup_auth.py
   ```

2. **Run the Agent Workflow**:

   ```bash
   # Execute the complete 7-step workflow
   python run_agent.py
   ```

3. **Verify Email Functionality**:
   ```bash
   # Test email integration
   python email_fix_summary.py
   ```

### 🌐 API Server Mode

1. **Start the FastAPI Server**:

   ```bash
   python start_api.py
   # Server will start on http://localhost:8000
   ```

2. **Access API Documentation**:

   ```bash
   # Open in browser
   http://localhost:8000/docs
   ```

3. **Test API Endpoints**:

   ```bash
   # Health check
   curl http://localhost:8000/health

   # Trigger workflow
   curl -X POST http://localhost:8000/agent/trigger-workflow

   # Chat with agent
   curl -X POST http://localhost:8000/agent/chat \
     -H "Content-Type: application/json" \
     -d '{"message": "Execute the post-meeting workflow"}'
   ```

### ⏰ Automated Scheduling

1. **Start Scheduler via API**:

   ```bash
   curl -X POST http://localhost:8000/agent/start-scheduler
   ```

2. **Check Scheduler Status**:

   ```bash
   curl http://localhost:8000/agent/scheduler-status
   ```

3. **Production Cron Setup**:
   ```bash
   # Add to crontab (runs every 30 minutes)
   */30 * * * * cd /path/to/agent-meeting-notes-main && python run_agent.py
   ```

## API Endpoints

| Endpoint                  | Method | Description                         |
| ------------------------- | ------ | ----------------------------------- |
| `/`                       | GET    | API root                            |
| `/health`                 | GET    | Health check                        |
| `/agent/trigger-workflow` | POST   | Execute the full workflow           |
| `/agent/workflow-status`  | GET    | Check workflow capabilities         |
| `/agent/start-scheduler`  | POST   | Start 30-minute recurring workflow  |
| `/agent/stop-scheduler`   | POST   | Stop automated scheduler            |
| `/agent/scheduler-status` | GET    | Check if scheduler is active        |
| `/agent/chat`             | POST   | LangChain interactive command agent |

## LangChain Agent Chat Interface

Supports natural language instructions:

- "Execute the Post meeting workflow"
- "Summarize the transcript for product-testing"
- "Send the meeting summary via email"
- "Attach the summary to today's event"

Example:

```bash
curl -X POST http://localhost:8000/agent/chat \
-H "Content-Type: application/json" \
-d '{"message": "Execute the workflow for the last 30 minutes"}'
```

## 🔄 Workflow Details

### Step 1: 🔍 Identify Meetings and Transcripts

- Uses **Google Calendar API** to identify recent meetings
- Uses **Google Drive API** to fetch corresponding transcript files
- Applies **fuzzy matching** on time and title for accurate pairing
- Prevents duplicate processing with database tracking

### Step 2: 🤖 AI Summarization

- Utilizes **Gemini Pro on Vertex AI** for intelligent analysis
- Outputs executive summary, action items, decisions, and outcomes
- Generates structured JSON and professional HTML formats
- Extracts key insights and meeting context

### Step 3: 📧 Email Distribution

- Sends **professional HTML summaries** via Gmail API
- Supports multiple recipients with personalized content
- Uses responsive email templates with company branding
- Includes meeting metadata and actionable insights

### Step 4: 💾 Google Drive Storage

- Uploads both JSON and HTML summary files
- **Organized by year/month/project** structure
- Maintains version control and file history
- Ensures secure access with proper permissions

### Step 5: 📅 Calendar Integration

- **Attaches summary files** to relevant Calendar events
- Updates meeting descriptions with summary links
- Maintains meeting context and follow-up tracking

### Step 6: 📁 File Management

- **Cleans up temporary files** and processing artifacts
- Organizes output directories by date and project
- Maintains system performance and storage efficiency

### Step 7: 📊 Google Sheets Task Extraction

- **Extracts action items** from meeting summaries
- Creates centralized **`meeting_task` sheet** per user
- Logs tasks with owner, deadline, and priority information
- Enables task tracking and project management integration

## Configuration

```bash
TIME_WINDOW_MINUTES=30
ENABLE_SCHEDULER=True
CLEANUP_TEMP_FILES=True
```

## File Organization

```
output/
├── html/
├── json/
└── Meeting_Summaries_HTML/
    └── 2025/
        └── 01/
            └── Project-X/
                ├── MeetingName.html
                └── MeetingName.json
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 🔐 Authentication Issues

**Problem**: `OAuth2 authentication failed`

```bash
# Solution: Re-run authentication setup
python setup_auth.py
```

**Problem**: `GMAIL_FROM_EMAIL environment variable not set`

```bash
# Solution: Check .env file has correct email
GMAIL_FROM_EMAIL=<EMAIL>
```

#### 📧 Email Issues

**Problem**: `Email not sending`

```bash
# Solution: Test email functionality
python email_fix_summary.py
```

#### 🗄️ Database Issues

**Problem**: `MySQL connection failed`

```bash
# Solution: Verify database credentials in .env
MYSQL_HOST=your-host
MYSQL_USERNAME=your-user
MYSQL_PASSWORD=your-password
```

#### 🤖 AI/LangChain Issues

**Problem**: `LangChain import errors`

```bash
# Solution: Install missing dependencies
pip install langchain langchain-google-vertexai
```

#### 🐍 Python Path Issues

**Problem**: `No Python at Python311`

```bash
# Solution: Fix Python path
python fix_python_path.py
```

### 📋 Verification Commands

```bash
# Test all integrations
python email_fix_summary.py

# Verify authentication
python -c "from src.services.utility.google_auth import GoogleAuthenticator; auth = GoogleAuthenticator(); print('✅ Auth working')"
```

## 🛠️ Available Tools

The Meeting Intelligence Agent includes **7 integrated LangChain tools**:

| Tool                          | Purpose            | Key Features                                            |
| ----------------------------- | ------------------ | ------------------------------------------------------- |
| **📅 CalendarTool**           | Meeting detection  | Identifies recent meetings, fetches attendee lists      |
| **💾 DriveTool**              | File management    | Uploads/downloads files, organizes by project structure |
| **🤖 SummarizerTool**         | AI processing      | Gemini Pro integration, JSON/HTML output generation     |
| **📧 NotificationTool**       | Email distribution | Professional templates, multi-recipient support         |
| **🔗 CalendarAttachmentTool** | Event integration  | Attaches summaries to calendar events                   |
| **📁 FileManagerTool**        | Cleanup operations | Temporary file management, directory organization       |
| **📊 SheetsToolLangChain**    | Task extraction    | Centralized task logging, action item tracking          |

### 📂 File Structure Overview

```
agent-meeting-notes-main/
├── src/
│   ├── agents/
│   │   └── langchain_meeting_agent.py    # Main agent orchestrator
│   ├── tools/                            # LangChain tool implementations
│   │   ├── langchain_calendar_tool.py
│   │   ├── langchain_drive_tool.py
│   │   ├── langchain_summarizer_tool.py
│   │   ├── langchain_notification_tool.py
│   │   ├── langchain_calendar_attachment_tool.py
│   │   ├── langchain_file_manager_tool.py
│   │   └── langchain_sheets_tool.py
│   ├── services/                         # Core business logic
│   │   ├── email_service.py             # Gmail API integration
│   │   ├── notification_service.py      # Email orchestration
│   │   ├── google_sheets_service.py     # Sheets API operations
│   │   ├── user_sheet_manager.py        # Centralized sheet management
│   │   └── utility/
│   │       └── google_auth.py           # OAuth2 authentication
│   └── api/                             # FastAPI endpoints
├── keys/                                # Authentication credentials
├── output/                              # Generated summaries
├── logs/                                # Application logs
├── client/
│   └── email_templates/                 # HTML email templates
├── run_agent.py                         # Main execution script
├── start_api.py                         # FastAPI server
├── setup_auth.py                        # Authentication setup
└── email_fix_summary.py                 # Email verification
```

## 🔐 Security

- **OAuth2 Authentication**: Secure Google API access with user consent
- **Service Account**: Server-to-server operations with limited scope
- **Credential Storage**: Encrypted keys stored in `./keys` directory
- **API Permissions**: Minimal required scopes for each service
- **Database Security**: Encrypted connections and parameterized queries

## Deployment

### Docker

```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "start_api.py"]
```

### Systemd

```ini
[Unit]
Description=Meeting Intelligence Agent
After=network.target

[Service]
Type=simple
User=meetingagent
WorkingDirectory=/path/to/project
ExecStart=/usr/bin/python start_api.py
Restart=always

[Install]
WantedBy=multi-user.target
```

## Monitoring & Logs

- `/health` endpoint for status
- `/agent/scheduler-status` for automation checks
- Logs stored in `./logging/`

## Future Enhancements

- Multi-tenant support
- Advanced meeting analytics
- Integration with external platforms
- Configurable summary formats
- Batch meeting processing

## License

This project is licensed under the MIT License. See the `LICENSE` file for details.

## Acknowledgments

- LangChain for agent orchestration
- Google Cloud for AI and API services
- FastAPI for robust web framework

## Documentation

- `quick-start.md` – Initial setup instructions
- `api-documentation.md` – Endpoint descriptions
- `deployment-guide.md` – Production deployment support
- `CHANGELOG.md` – Version history and updates

---
