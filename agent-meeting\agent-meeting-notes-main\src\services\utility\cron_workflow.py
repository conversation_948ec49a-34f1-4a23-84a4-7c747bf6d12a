#!/usr/bin/env python3
"""
Meeting Intelligence Agent - Automated Cron Job Workflow

This script runs the Post meeting intelligence workflow automatically every 30 minutes.
It's designed to be integrated with the existing codebase and use the same agent infrastructure.

Usage:
    python cron_workflow.py           # Run the workflow
    python cron_workflow.py --test    # Test mode (validate setup only)

Cron entry (every 30 minutes):
    */30 * * * * cd /d/sopna_elevatio_ai/meeting-intelligence-agent && python cron_workflow.py >> logging/cron.log 2>&1
"""

import os
import sys
import asyncio
import logging
import argparse
from datetime import datetime
from pathlib import Path

# Set UTF-8 encoding for Windows compatibility

# Get the current script directory and go up two levels to project root
SCRIPT_DIR = Path(__file__).parent.parent.parent.absolute()

# Add the project root directory to Python path
sys.path.insert(0, str(SCRIPT_DIR))

# Configure logging for cron job
from src.constants.app import (
    APP_NAME, APP_DESCRIPTION, APP_VERSION, LOG_FORMAT, LOG_DATE_FORMAT, LOG_FILE_PATH, LOG_LEVELS
)

log_dir = SCRIPT_DIR / "logging"
log_dir.mkdir(exist_ok=True)

logging.basicConfig(
    level=LOG_LEVELS['INFO'],
    format=LOG_FORMAT,
    datefmt=LOG_DATE_FORMAT,
    handlers=[
        logging.FileHandler(LOG_FILE_PATH),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def setup_environment():
    """Set up environment variables for elevation-agent-dev project."""
    # Change to the project root directory
    os.chdir(SCRIPT_DIR)
    
    # Set up environment variables for your specific project
    env_vars = {
        'GOOGLE_PROJECT_ID': 'elevation-agent-dev',
        'VERTEX_AI_LOCATION': 'us-central1',
        'GOOGLE_APPLICATION_CREDENTIALS': str(SCRIPT_DIR / 'keys' / 'google-service-account.json'),
        'GMAIL_CREDENTIALS_PATH': str(SCRIPT_DIR / 'keys' / 'gmail-credentials.json'),
        'GMAIL_TOKEN_PATH': str(SCRIPT_DIR / 'keys' / 'gmail-token.json'),
        'EMAIL_PROVIDER': 'gmail',
        'PYTHONPATH': str(SCRIPT_DIR / 'src')
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
    
    logger.info(f"[SETUP] Environment configured for {env_vars['GOOGLE_PROJECT_ID']}")

def check_dependencies():
    """Check if all required dependencies are available."""
    required_files = [
        SCRIPT_DIR / 'src' / 'agents' / 'langchain_meeting_agent.py',
        # OAuth2 credentials (either old gmail-credentials.json or new google-oauth-credentials.json)
        # SCRIPT_DIR / 'keys' / 'google-oauth-credentials.json',  # New OAuth2 file
    ]

    # Check for OAuth2 credentials (flexible - either old or new format)
    oauth_creds_found = False
    oauth_paths = [
        SCRIPT_DIR / 'keys' / 'google-oauth-credentials.json',  # New unified OAuth2
        SCRIPT_DIR / 'keys' / 'gmail-credentials.json',         # Legacy OAuth2
    ]

    for oauth_path in oauth_paths:
        if oauth_path.exists():
            oauth_creds_found = True
            logger.info(f"[SUCCESS] Found OAuth2 credentials: {oauth_path}")
            break

    if not oauth_creds_found:
        logger.error("[ERROR] No OAuth2 credentials found. Need either:")
        for path in oauth_paths:
            logger.error(f"  - {path}")
        return False
    
    for file_path in required_files:
        if not file_path.exists():
            logger.error(f"[ERROR] Required file missing: {file_path}")
            return False
    
    try:
        # Test import of the main agent module
        from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow
        logger.info("[SUCCESS] LangChain meeting agent imported successfully")
        return True
    except ImportError as e:
        logger.error(f"[ERROR] Failed to import meeting agent: {e}")
        return False

async def run_5_step_workflow():
    """Run the Post meeting intelligence workflow."""
    logger.info(f"[WORKFLOW] Starting {APP_NAME} v{APP_VERSION} - {APP_DESCRIPTION}")
    
    try:
        # Import and run the autonomous meeting workflow
        from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow
        
        # Run the workflow
        result = await run_autonomous_meeting_workflow()
        
        logger.info("[SUCCESS] Post meetingworkflow completed successfully")
        return result
        
    except Exception as e:
        logger.error(f"[ERROR] Workflow execution failed: {e}")
        raise

def log_workflow_statistics(result, duration):
    """Log workflow statistics to a separate stats file."""
    stats_file = SCRIPT_DIR / 'logging' / 'workflow_stats.log'
    
    try:
        with open(stats_file, 'a') as f:
            timestamp = datetime.now().isoformat()
            f.write(f"{timestamp} | Duration: {duration} | Result: {result}\n")
        
        logger.info(f"[STATS] Workflow statistics logged to {stats_file}")
        
    except Exception as e:
        logger.warning(f"[WARNING] Failed to log statistics: {e}")

def test_mode():
    """Test mode - validate setup without running the workflow."""
    logger.info("[TEST] Running in test mode - validating setup only")
    
    try:
        # Setup environment
        setup_environment()
        
        # Check dependencies
        if not check_dependencies():
            logger.error("[ERROR] Test failed - dependency check failed")
            return False
        
        # Try to import the workflow function
        from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow
        
        logger.info("[SUCCESS] Test mode completed - all dependencies available")
        return True
        
    except Exception as e:
        logger.error(f"[ERROR] Test mode failed: {e}")
        return False

def main():
    """Main function for automated cron job execution."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Meeting Intelligence Agent Cron Workflow')
    parser.add_argument('--test', action='store_true', help='Test mode - validate setup only')
    args = parser.parse_args()
    
    start_time = datetime.now()
    
    if args.test:
        logger.info(f"[TEST] Meeting Intelligence Test Mode started at {start_time}")
        logger.info(f"[INFO] Working directory: {SCRIPT_DIR}")
        
        if test_mode():
            logger.info("[SUCCESS] Test mode completed successfully")
            return 0
        else:
            logger.error("[ERROR] Test mode failed")
            return 1
    else:
        logger.info(f"[START] Meeting Intelligence Automated Workflow started at {start_time}")
        logger.info(f"[INFO] Working directory: {SCRIPT_DIR}")
        
        try:
            # Setup environment
            setup_environment()
            
            # Check dependencies
            if not check_dependencies():
                logger.error("[ERROR] Dependency check failed - cannot proceed")
                return 1
            
            # Run the Post meetingworkflow
            result = asyncio.run(run_5_step_workflow())
            
            # Calculate duration and log statistics
            end_time = datetime.now()
            duration = end_time - start_time
            
            log_workflow_statistics(result, duration)
            
            logger.info(f"[SUCCESS] Automated workflow completed successfully in {duration}")
            logger.info("=" * 60)
            
            return 0
            
        except Exception as e:
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.error(f"[ERROR] Automated workflow failed after {duration}: {e}")
            logger.error("=" * 60)
            
            return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 