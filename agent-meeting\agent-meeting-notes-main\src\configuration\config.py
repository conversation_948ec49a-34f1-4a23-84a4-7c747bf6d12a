# config.py

import os
from dotenv import load_dotenv
from urllib.parse import quote_plus

load_dotenv()

# App Info
APP_NAME = "Meeting Intelligence Agent"
APP_VERSION = "1.0.0"
APP_ENV = os.getenv("APP_ENV", "development")

# Debug & Logging
DEBUG = os.getenv("DEBUG", "False") == "True"
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
LOG_FILE_PATH = os.getenv("LOG_FILE_PATH", "logs/app.log")

# Database
DB_HOST = os.getenv("MYSQL_HOST", "*************")
DB_PORT = os.getenv("MYSQL_PORT", "3306")
DB_USER = os.getenv("MYSQL_USERNAME", "root")
DB_PASSWORD = os.getenv("MYSQL_PASSWORD", "elevationaiAgent002")
DB_NAME = os.getenv("MYSQL_DATABASE", "meeting-intelligence")

# Safe password encoding for special characters
safe_password = quote_plus(DB_PASSWORD)

# SQLAlchemy-compatible DB URL
DB_URL = f"mysql+pymysql://{DB_USER}:{safe_password}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Google OAuth2 Credentials (Unified for Gmail, Calendar, Drive)
GOOGLE_OAUTH_CREDENTIALS_PATH = os.getenv("GOOGLE_OAUTH_CREDENTIALS_PATH", "./keys/google-oauth-credentials.json")
GOOGLE_TOKEN_PATH = os.getenv("GOOGLE_TOKEN_PATH", "./keys/google-token.json")

# Legacy paths for backward compatibility (deprecated)
GMAIL_CREDENTIALS_PATH = os.getenv("GMAIL_CREDENTIALS_PATH", GOOGLE_OAUTH_CREDENTIALS_PATH)
GMAIL_TOKEN_PATH = os.getenv("GMAIL_TOKEN_PATH", GOOGLE_TOKEN_PATH)
GOOGLE_APPLICATION_CREDENTIALS = os.getenv("GOOGLE_APPLICATION_CREDENTIALS", "./keys/google-service-account.json")  # Deprecated

# Scheduling & Vertex AI
TIME_WINDOW_MINUTES = int(os.getenv("TIME_WINDOW_MINUTES", 30))
VERTEX_AI_LOCATION = os.getenv("VERTEX_AI_LOCATION", "us-central1")
