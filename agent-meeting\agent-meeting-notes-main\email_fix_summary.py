#!/usr/bin/env python3
"""
Email Fix Summary and Final Verification
"""

import sys
import os
import asyncio
from datetime import datetime

# Add src to path
sys.path.append('src')

# Load environment
from dotenv import load_dotenv
load_dotenv()

def print_header(title):
    """Print a formatted header."""
    print("\n" + "=" * 60)
    print(f"🔧 {title}")
    print("=" * 60)

def print_success(message):
    """Print a success message."""
    print(f"✅ {message}")

def print_error(message):
    """Print an error message."""
    print(f"❌ {message}")

def print_info(message):
    """Print an info message."""
    print(f"📋 {message}")

def verify_email_service():
    """Verify the email service is working."""
    print_header("Email Service Verification")
    
    try:
        from src.services.utility.google_auth import GoogleAuthenticator
        from src.services.email_service import EmailService
        
        # Initialize services
        auth = GoogleAuthenticator()
        email_service = EmailService(provider="gmail", google_auth=auth)
        
        # Test email sending
        async def send_test():
            return await email_service.send_email(
                to_email="<EMAIL>",
                subject="✅ Email Fix Verification - Meeting Intelligence Agent",
                body="Email service is working correctly!",
                html_body="""
                <html>
                <body style="font-family: Arial, sans-serif;">
                    <h2>✅ Email Fix Verification</h2>
                    <p>This email confirms that the Meeting Intelligence Agent email functionality has been successfully fixed!</p>
                    <h3>🔧 Issues Fixed:</h3>
                    <ul>
                        <li>✅ Environment variable loading (GMAIL_FROM_EMAIL)</li>
                        <li>✅ Async/sync compatibility in NotificationService</li>
                        <li>✅ LangChain tool integration</li>
                        <li>✅ Gmail API authentication</li>
                    </ul>
                    <p><strong>Timestamp:</strong> {}</p>
                    <p><strong>Status:</strong> 🎉 All email functionality operational!</p>
                </body>
                </html>
                """.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            )
        
        result = asyncio.run(send_test())
        
        if result.get('status') == 'success':
            print_success("Email service is working correctly")
            print_info(f"Message ID: {result.get('message_id')}")
            return True
        else:
            print_error(f"Email service failed: {result}")
            return False
            
    except Exception as e:
        print_error(f"Email service verification failed: {e}")
        return False

def verify_notification_service():
    """Verify the notification service is working."""
    print_header("Notification Service Verification")
    
    try:
        from src.services.utility.google_auth import GoogleAuthenticator
        from src.services.email_service import EmailService
        from src.services.notification_service import NotificationService
        
        # Initialize services
        auth = GoogleAuthenticator()
        email_service = EmailService(provider="gmail", google_auth=auth)
        notification_service = NotificationService(email_service=email_service)
        
        # Test meeting summary email
        result = notification_service.send_meeting_summary_email(
            recipients=["<EMAIL>"],
            meeting_title="✅ Email Fix Verification Meeting",
            meeting_date=datetime.now().strftime('%B %d, %Y'),
            summary_html="""
            <div style="font-family: Arial, sans-serif;">
                <h3>🔧 Email Fix Verification Meeting</h3>
                <p>This meeting summary confirms that the notification service is working correctly after the email fixes.</p>
                
                <h4>📋 Issues Resolved:</h4>
                <ul>
                    <li>✅ Fixed async/sync compatibility</li>
                    <li>✅ Resolved environment variable loading</li>
                    <li>✅ Updated NotificationTool constructor</li>
                    <li>✅ Verified Gmail API integration</li>
                </ul>
                
                <h4>🎯 Verification Results:</h4>
                <ul>
                    <li>✅ Email service operational</li>
                    <li>✅ Notification service functional</li>
                    <li>✅ LangChain integration working</li>
                    <li>✅ Agent workflow Step 4 fixed</li>
                </ul>
            </div>
            """,
            summary_json={
                "meeting_title": "Email Fix Verification Meeting",
                "attendees": ["<EMAIL>"],
                "executive_summary": "All email functionality has been successfully fixed and verified.",
                "outcomes": [
                    {
                        "decision": "Email system is fully operational",
                        "owner": "Meeting Intelligence Agent",
                        "rationale": "All tests passed successfully"
                    }
                ]
            }
        )
        
        if result.get('success'):
            print_success("Notification service is working correctly")
            print_info(f"Sent to: {result.get('successful_recipients')}")
            return True
        else:
            print_error(f"Notification service failed: {result}")
            return False
            
    except Exception as e:
        print_error(f"Notification service verification failed: {e}")
        return False

def verify_langchain_tool():
    """Verify the LangChain notification tool is working."""
    print_header("LangChain Tool Verification")
    
    try:
        from src.tools.langchain_notification_tool import NotificationTool
        from src.services.utility.google_auth import GoogleAuthenticator
        from src.services.email_service import EmailService
        from src.services.notification_service import NotificationService
        
        # Initialize services
        auth = GoogleAuthenticator()
        email_service = EmailService(provider="gmail", google_auth=auth)
        notification_service = NotificationService(email_service=email_service)
        
        # Initialize notification tool
        notification_tool = NotificationTool(notification_service=notification_service)
        
        # Test the tool
        test_query = """
        Send meeting <NAME_EMAIL> for meeting titled "LangChain Tool Verification".
        
        Summary: This is a verification test for the LangChain notification tool integration.
        The email functionality has been successfully fixed and is now operational.
        """
        
        result = notification_tool._run(test_query)
        
        if "success" in result.lower():
            print_success("LangChain notification tool is working correctly")
            print_info("Tool integration verified")
            return True
        else:
            print_error(f"LangChain tool failed: {result}")
            return False
            
    except Exception as e:
        print_error(f"LangChain tool verification failed: {e}")
        return False

def main():
    """Main verification function."""
    print_header("Meeting Intelligence Agent - Email Fix Verification")
    print_info("Verifying all email functionality after fixes...")
    
    # Run all verifications
    email_ok = verify_email_service()
    notification_ok = verify_notification_service()
    langchain_ok = verify_langchain_tool()
    
    # Final summary
    print_header("Final Verification Results")
    
    print(f"Email Service:        {'✅ WORKING' if email_ok else '❌ FAILED'}")
    print(f"Notification Service: {'✅ WORKING' if notification_ok else '❌ FAILED'}")
    print(f"LangChain Tool:       {'✅ WORKING' if langchain_ok else '❌ FAILED'}")
    
    if email_ok and notification_ok and langchain_ok:
        print("\n🎉 ALL EMAIL FUNCTIONALITY IS NOW WORKING PERFECTLY!")
        print("📧 The Meeting Intelligence Agent can now send emails successfully!")
        print("✅ Step 4 of the agent workflow (email notification) is operational!")
        print("\n📋 Summary of fixes applied:")
        print("   • Fixed environment variable loading with dotenv")
        print("   • Resolved async/sync compatibility in NotificationService")
        print("   • Updated NotificationTool constructor to accept parameters")
        print("   • Verified Gmail API authentication and permissions")
        print("   • Tested complete email workflow end-to-end")
    else:
        print("\n⚠️  Some email functionality is still not working")
        print("   Please check the error messages above for details")

if __name__ == "__main__":
    main()
