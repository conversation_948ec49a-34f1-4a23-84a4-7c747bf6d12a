2025-07-28 16:32:21 - startup - INFO -  Logging initialized at level: DEBUG
2025-07-28 16:32:22 - watchfiles.main - DEBUG - 9 changes detected: {(<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__\\__init__.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__\\main.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__\\__init__.cpython-312.pyc.2285134259792'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__\\__init__.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__\\__init__.cpython-312.pyc.2285134259792'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__\\main.cpython-312.pyc.2285134124240'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__\\main.cpython-312.pyc.2285134124240'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__\\main.cpython-312.pyc')}
2025-07-28 16:32:23 - watchfiles.main - DEBUG - 18 changes detected: {(<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\routers\\__pycache__\\__init__.cpython-312.pyc'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\routers\\__pycache__'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\routers\\__pycache__\\agent.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\routers\\__pycache__\\agent.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__\\langchain_meeting_agent.cpython-312.pyc.2285148173776'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\routers\\__pycache__\\agent.cpython-312.pyc.2285149127504'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__\\langchain_meeting_agent.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__\\langchain_meeting_agent.cpython-312.pyc.2285148173776'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\routers\\__pycache__\\__init__.cpython-312.pyc'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\routers\\__pycache__\\agent.cpython-312.pyc.2285149127504'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__\\__init__.cpython-312.pyc.2285149138384'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\routers\\__pycache__\\__init__.cpython-312.pyc.2285148765552'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\routers\\__pycache__\\__init__.cpython-312.pyc.2285148765552'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__\\__init__.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__\\langchain_meeting_agent.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__\\__init__.cpython-312.pyc.2285149138384'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__\\__init__.cpython-312.pyc')}
2025-07-28 16:32:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:36 - watchfiles.main - DEBUG - 19 changes detected: {(<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\__init__.cpython-312.pyc.2285787307248'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\__init__.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\__init__.cpython-312.pyc.2285820813648'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_calendar_tool.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\ai_summarizer.cpython-312.pyc'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\__init__.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\ai_summarizer.cpython-312.pyc.2285820814128'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\ai_summarizer.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_calendar_tool.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\ai_summarizer.cpython-312.pyc.2285820814128'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_calendar_tool.cpython-312.pyc.2285819034624'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\__init__.cpython-312.pyc'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_calendar_tool.cpython-312.pyc.2285819034624'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\__init__.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\__init__.cpython-312.pyc.2285787307248'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\__init__.cpython-312.pyc.2285820813648')}
2025-07-28 16:32:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:38 - watchfiles.main - DEBUG - 6 changes detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\email_service.cpython-312.pyc.2285830752976'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\email_service.cpython-312.pyc.2285830752976'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\email_service.cpython-312.pyc'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\email_service.cpython-312.pyc')}
2025-07-28 16:32:38 - watchfiles.main - DEBUG - 10 changes detected: {(<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\__init__.cpython-312.pyc.2285831751264'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\google_auth.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\google_auth.cpython-312.pyc.2285831752496'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\__init__.cpython-312.pyc.2285831751264'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\__init__.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\google_auth.cpython-312.pyc.2285831752496'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\__init__.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\google_auth.cpython-312.pyc'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:39 - watchfiles.main - DEBUG - 45 changes detected: {(<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__\\models.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\helpers\\__pycache__\\file_utils.cpython-312.pyc.2285833749488'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_drive_tool.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_calendar_attachment_tool.cpython-312.pyc.2285832561712'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_notification_tool.cpython-312.pyc.2285832646032'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\notification_service.cpython-312.pyc.2285832643744'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\helpers\\__pycache__\\__init__.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__\\models.cpython-312.pyc.2285149138704'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_drive_tool.cpython-312.pyc.2285832644976'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_summarizer_tool.cpython-312.pyc.2285832645856'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_drive_tool.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_file_manager_tool.cpython-312.pyc'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\notification_service.cpython-312.pyc.2285832643744'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_notification_tool.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_calendar_attachment_tool.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__\\models.cpython-312.pyc.2285149138704'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_file_manager_tool.cpython-312.pyc.2285832646736'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\helpers\\__pycache__\\__init__.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\calendar_service.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_drive_tool.cpython-312.pyc.2285832644976'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_summarizer_tool.cpython-312.pyc.2285832645856'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_file_manager_tool.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\calendar_service.cpython-312.pyc.2285832641808'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\helpers\\__pycache__\\__init__.cpython-312.pyc.2285833749168'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_notification_tool.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_file_manager_tool.cpython-312.pyc.2285832646736'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\calendar_service.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_summarizer_tool.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\calendar_service.cpython-312.pyc.2285832641808'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\helpers\\__pycache__'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\helpers\\__pycache__\\__init__.cpython-312.pyc.2285833749168'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_summarizer_tool.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\notification_service.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\helpers\\__pycache__\\file_utils.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\api\\__pycache__\\models.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\helpers\\__pycache__\\file_utils.cpython-312.pyc.2285833749488'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_calendar_attachment_tool.cpython-312.pyc.2285832561712'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\tools\\__pycache__\\langchain_notification_tool.cpython-312.pyc.2285832646032'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\__pycache__\\notification_service.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\helpers\\__pycache__\\file_utils.cpython-312.pyc')}
2025-07-28 16:32:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:32:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:33:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:34:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:04 - watchfiles.main - DEBUG - 2 changes detected: {(<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\test_agent_basic.py'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:35:57 - watchfiles.main - DEBUG - 7 changes detected: {(<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\configuration\\__pycache__\\config.cpython-312.pyc'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\client'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\configuration\\__pycache__\\config.cpython-312.pyc.1843575459472'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\configuration\\__pycache__'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\configuration\\__pycache__\\config.cpython-312.pyc'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\configuration\\__pycache__\\config.cpython-312.pyc.1843575459472')}
2025-07-28 16:36:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:36:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:37:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:38:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:39:54 - watchfiles.main - DEBUG - 2 changes detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\setup_authentication.py')}
2025-07-28 16:40:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:41:09 - watchfiles.main - DEBUG - 3 changes detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\keys'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\keys\\google-token.json.backup'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\keys\\google-token.json')}
2025-07-28 16:42:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\fix_tls_certificates.py')}
2025-07-28 16:43:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\.env')}
2025-07-28 16:44:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\google_auth.py')}
2025-07-28 16:46:30 - watchfiles.main - DEBUG - 8 changes detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\keys'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\google_auth.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\google_auth.cpython-312.pyc.1724201243328'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\google_auth.py'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\google_auth.cpython-312.pyc.1724201243328'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\keys\\google-token.json'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\services\\utility\\__pycache__\\google_auth.cpython-312.pyc')}
2025-07-28 16:47:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\langchain_meeting_agent.py')}
2025-07-28 16:48:34 - watchfiles.main - DEBUG - 5 changes detected: {(<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__\\langchain_meeting_agent.cpython-312.pyc'), (<Change.deleted: 3>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__\\langchain_meeting_agent.cpython-312.pyc.1816215292720'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__\\langchain_meeting_agent.cpython-312.pyc'), (<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\src\\agents\\__pycache__\\langchain_meeting_agent.cpython-312.pyc.1816215292720')}
2025-07-28 16:48:34 - startup - INFO -  Logging initialized at level: INFO
2025-07-28 16:48:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:36 - src.services.utility.google_auth - INFO - SSL certificates configured: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\certifi\cacert.pem
2025-07-28 16:48:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:36 - src.api.main - INFO - Post Meeting Intelligence Agent API starting up...
2025-07-28 16:48:36 - src.api.main - INFO - Technology Stack: FastAPI + LangChain + Vertex AI + MySQL + Email
2025-07-28 16:48:36 - src.api.main - INFO - Post meeting Intelligence Agent API shutting down...
2025-07-28 16:48:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:48:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:40 - watchfiles.main - DEBUG - 2 changes detected: {(<Change.added: 1>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\test_authenticated_agent.py'), (<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:49:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:33 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:34 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:35 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:36 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:37 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:38 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:39 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:40 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:41 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:42 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:43 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:44 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:45 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:46 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:47 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:48 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:49 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:50 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:51 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:52 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:53 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:54 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:55 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:56 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:57 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:58 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:50:59 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:00 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:01 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:02 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:03 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:04 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:05 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:06 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:07 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:08 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:09 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:10 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:11 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:12 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:13 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:14 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:15 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:16 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:17 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:18 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:19 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:20 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:21 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:22 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:23 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:24 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:25 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:26 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:27 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:28 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:29 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:30 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:31 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
2025-07-28 16:51:32 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'C:\\Users\\<USER>\\Downloads\\agent-meeting\\agent-meeting-notes-main\\logging\\api.log')}
