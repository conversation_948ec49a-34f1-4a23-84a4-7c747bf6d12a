"""Google Sheets service for Meeting Intelligence Agent task management."""

import os
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from googleapiclient.errors import HttpError
from src.services.utility.google_auth import GoogleAuthenticator
from src.services.user_sheet_manager import UserSheetManager

logger = logging.getLogger(__name__)


class GoogleSheetsService:
    """Service for managing meeting tasks in Google Sheets."""

    def __init__(self, authenticator: GoogleAuthenticator):
        """
        Initialize Sheets service.

        Args:
            authenticator: Google authenticator instance
        """
        self.authenticator = authenticator
        self.service = authenticator.get_sheets_service()
        self.template_id = os.getenv('GOOGLE_SHEETS_TEMPLATE_ID')
        self.folder_id = os.getenv('SHEETS_FOLDER_ID')

        # Initialize centralized user sheet manager
        self.user_sheet_manager = UserSheetManager(authenticator)

        if not self.service:
            logger.error("Failed to initialize Google Sheets service")

    def ensure_user_meeting_task_sheet(self, user_email: str = None) -> Optional[str]:
        """
        Ensure the authenticated user has a central meeting_task sheet.
        Creates one if it doesn't exist.

        Args:
            user_email: User email (if None, will get from auth)

        Returns:
            Sheet ID if successful, None otherwise
        """
        try:
            return self.user_sheet_manager.create_meeting_task_sheet_if_not_exists(user_email)
        except Exception as e:
            logger.error(f"Error ensuring user meeting_task sheet: {e}")
            return None

    def write_tasks_from_ai_summary(self, ai_output: Dict[str, Any], user_email: str = None) -> bool:
        """
        Extract tasks from AI summary and write to user's central meeting_task sheet.

        Args:
            ai_output: AI summarizer output
            user_email: User email (if None, will get from auth)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure user has a meeting_task sheet
            sheet_id = self.ensure_user_meeting_task_sheet(user_email)
            if not sheet_id:
                logger.error("Could not ensure user meeting_task sheet exists")
                return False

            # Extract tasks from AI output
            tasks = self.user_sheet_manager.extract_tasks_from_summary(ai_output)
            if not tasks:
                logger.info("No tasks found in AI output")
                return True  # Not an error, just no tasks

            # Append tasks to sheet
            success = self.user_sheet_manager.append_tasks_to_sheet(sheet_id, tasks)

            if success:
                logger.info(f"Successfully wrote {len(tasks)} tasks to user's meeting_task sheet")

            return success

        except Exception as e:
            logger.error(f"Error writing tasks from AI summary: {e}")
            return False

    def get_user_sheet_info(self, user_email: str = None) -> Optional[Dict[str, str]]:
        """
        Get information about the user's meeting_task sheet.

        Args:
            user_email: User email (if None, will get from auth)

        Returns:
            Dict with sheet_id and sheet_url, or None if not found
        """
        try:
            if not user_email:
                user_email = self.authenticator.get_user_email()
                if not user_email:
                    logger.error("Could not determine user email")
                    return None

            sheet_id = self.user_sheet_manager.get_user_sheet_id(user_email)
            if sheet_id:
                return {
                    'sheet_id': sheet_id,
                    'sheet_url': f'https://docs.google.com/spreadsheets/d/{sheet_id}',
                    'user_email': user_email
                }
            else:
                return None

        except Exception as e:
            logger.error(f"Error getting user sheet info: {e}")
            return None

    def create_sheet_if_not_exists(self, user_id: str, sheet_name: str = "Meeting Tasks") -> Optional[str]:
        """
        Create a new Google Sheet for the user if one doesn't exist.
        
        Args:
            user_id: User identifier (email or ID)
            sheet_name: Name for the new sheet
            
        Returns:
            Spreadsheet ID if successful, None otherwise
        """
        try:
            # Create new spreadsheet
            spreadsheet_body = {
                'properties': {
                    'title': f"{sheet_name} - {user_id}",
                    'locale': 'en_US',
                    'timeZone': 'UTC'
                },
                'sheets': [{
                    'properties': {
                        'title': 'Tasks',
                        'gridProperties': {
                            'rowCount': 1000,
                            'columnCount': 10
                        }
                    }
                }]
            }
            
            spreadsheet = self.service.spreadsheets().create(
                body=spreadsheet_body
            ).execute()
            
            spreadsheet_id = spreadsheet['spreadsheetId']
            logger.info(f"Created new spreadsheet: {spreadsheet_id}")
            
            # Set up headers
            self._setup_headers(spreadsheet_id)
            
            # Move to folder if specified
            if self.folder_id:
                self._move_to_folder(spreadsheet_id, self.folder_id)
            
            return spreadsheet_id
            
        except HttpError as e:
            logger.error(f"Failed to create spreadsheet: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error creating spreadsheet: {e}")
            return None

    def _setup_headers(self, spreadsheet_id: str) -> bool:
        """
        Set up headers for the task sheet.
        
        Args:
            spreadsheet_id: ID of the spreadsheet
            
        Returns:
            True if successful, False otherwise
        """
        try:
            headers = [
                ['Task Description', 'Priority', 'Assigned To', 'Deadline', 'Meeting Title', 'Date Added', 'Status', 'Owner', 'Context']
            ]
            
            # Add headers
            self.service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range='Tasks!A1:I1',
                valueInputOption='RAW',
                body={'values': headers}
            ).execute()
            
            # Format headers
            self._format_headers(spreadsheet_id)
            
            logger.info("Headers set up successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set up headers: {e}")
            return False

    def _format_headers(self, spreadsheet_id: str):
        """Format the header row with bold text and background color."""
        try:
            requests = [{
                'repeatCell': {
                    'range': {
                        'sheetId': 0,
                        'startRowIndex': 0,
                        'endRowIndex': 1,
                        'startColumnIndex': 0,
                        'endColumnIndex': 9
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'backgroundColor': {
                                'red': 0.9,
                                'green': 0.9,
                                'blue': 0.9
                            },
                            'textFormat': {
                                'bold': True,
                                'fontSize': 11
                            }
                        }
                    },
                    'fields': 'userEnteredFormat(backgroundColor,textFormat)'
                }
            }]
            
            self.service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body={'requests': requests}
            ).execute()
            
        except Exception as e:
            logger.error(f"Failed to format headers: {e}")

    def _move_to_folder(self, spreadsheet_id: str, folder_id: str):
        """Move spreadsheet to specified folder."""
        try:
            drive_service = self.authenticator.get_drive_service()
            if drive_service:
                # Add to folder
                drive_service.files().update(
                    fileId=spreadsheet_id,
                    addParents=folder_id,
                    fields='id, parents'
                ).execute()
                logger.info(f"Moved spreadsheet to folder: {folder_id}")
        except Exception as e:
            logger.error(f"Failed to move spreadsheet to folder: {e}")

    def read_range(self, sheet_id: str, range_name: str) -> List[List[str]]:
        """
        Read data from a specific range in the sheet.
        
        Args:
            sheet_id: Spreadsheet ID
            range_name: Range to read (e.g., 'Tasks!A1:D10')
            
        Returns:
            List of rows with cell values
        """
        try:
            result = self.service.spreadsheets().values().get(
                spreadsheetId=sheet_id,
                range=range_name
            ).execute()
            
            values = result.get('values', [])
            logger.info(f"Read {len(values)} rows from range {range_name}")
            return values
            
        except HttpError as e:
            logger.error(f"Failed to read range {range_name}: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error reading range: {e}")
            return []

    def append_rows(self, sheet_id: str, rows: List[List[str]], range_name: str = "Tasks!A:I") -> bool:
        """
        Append rows to the sheet.
        
        Args:
            sheet_id: Spreadsheet ID
            rows: List of rows to append
            range_name: Range to append to
            
        Returns:
            True if successful, False otherwise
        """
        try:
            body = {
                'values': rows
            }
            
            result = self.service.spreadsheets().values().append(
                spreadsheetId=sheet_id,
                range=range_name,
                valueInputOption='RAW',
                insertDataOption='INSERT_ROWS',
                body=body
            ).execute()
            
            logger.info(f"Appended {len(rows)} rows to sheet")
            return True
            
        except HttpError as e:
            logger.error(f"Failed to append rows: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error appending rows: {e}")
            return False

    def update_cell(self, sheet_id: str, cell_range: str, value: str) -> bool:
        """
        Update a specific cell in the sheet.
        
        Args:
            sheet_id: Spreadsheet ID
            cell_range: Cell range (e.g., 'Tasks!A1')
            value: Value to set
            
        Returns:
            True if successful, False otherwise
        """
        try:
            body = {
                'values': [[value]]
            }
            
            self.service.spreadsheets().values().update(
                spreadsheetId=sheet_id,
                range=cell_range,
                valueInputOption='RAW',
                body=body
            ).execute()
            
            logger.info(f"Updated cell {cell_range} with value: {value}")
            return True
            
        except HttpError as e:
            logger.error(f"Failed to update cell {cell_range}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error updating cell: {e}")
            return False

    def extract_tasks_from_ai_output(self, ai_output: Dict[str, Any]) -> List[List[str]]:
        """
        Extract task data from AI summarizer JSON output.
        
        Args:
            ai_output: AI summarizer output containing outcomes with actions
            
        Returns:
            List of task rows ready for spreadsheet insertion
        """
        tasks = []
        
        try:
            # Get meeting metadata
            meeting_title = ai_output.get('meeting_title', 'Unknown Meeting')
            date_added = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Extract tasks from outcomes
            outcomes = ai_output.get('outcomes', [])
            
            for outcome in outcomes:
                if isinstance(outcome, dict):
                    # Get outcome context
                    decision = outcome.get('decision', '')
                    owner = outcome.get('owner', 'Unassigned')
                    context = outcome.get('context', '')
                    
                    # Extract actions from this outcome
                    actions = outcome.get('actions', [])
                    
                    for action in actions:
                        if isinstance(action, dict):
                            task_row = [
                                action.get('task', 'No task description'),  # Task Description
                                action.get('priority', 'MEDIUM'),           # Priority
                                action.get('owner', owner),                 # Assigned To
                                action.get('deadline', 'Not specified'),   # Deadline
                                meeting_title,                              # Meeting Title
                                date_added,                                 # Date Added
                                'Open',                                     # Status
                                owner,                                      # Owner (from outcome)
                                f"{decision} | {context}"[:200]             # Context (truncated)
                            ]
                            tasks.append(task_row)
            
            logger.info(f"Extracted {len(tasks)} tasks from AI output")
            return tasks
            
        except Exception as e:
            logger.error(f"Failed to extract tasks from AI output: {e}")
            return []

    def write_tasks_to_sheet(self, sheet_id: str, ai_output: Dict[str, Any]) -> bool:
        """
        Extract tasks from AI output and write them to the sheet.
        
        Args:
            sheet_id: Spreadsheet ID
            ai_output: AI summarizer output
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Extract tasks
            tasks = self.extract_tasks_from_ai_output(ai_output)
            
            if not tasks:
                logger.warning("No tasks found in AI output")
                return True  # Not an error, just no tasks
            
            # Append tasks to sheet
            success = self.append_rows(sheet_id, tasks)
            
            if success:
                logger.info(f"Successfully wrote {len(tasks)} tasks to sheet {sheet_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to write tasks to sheet: {e}")
            return False

    def get_sheet_url(self, sheet_id: str) -> str:
        """
        Get the web URL for the spreadsheet.
        
        Args:
            sheet_id: Spreadsheet ID
            
        Returns:
            Web URL for the spreadsheet
        """
        return f"https://docs.google.com/spreadsheets/d/{sheet_id}/edit"

    def test_connection(self) -> bool:
        """Test the Sheets service connection."""
        try:
            if not self.service:
                return False
            
            # Try to create a test spreadsheet and delete it
            test_sheet = self.service.spreadsheets().create(
                body={'properties': {'title': 'Test Sheet - Delete Me'}}
            ).execute()
            
            test_id = test_sheet['spreadsheetId']
            
            # Delete the test sheet using Drive API
            drive_service = self.authenticator.get_drive_service()
            if drive_service:
                drive_service.files().delete(fileId=test_id).execute()
            
            logger.info("Sheets service connection test passed")
            return True
            
        except Exception as e:
            logger.error(f"Sheets service connection test failed: {e}")
            return False
