"""Simple demonstration of Google Sheets integration."""

import json
from datetime import datetime


def demo_task_extraction():
    """Demonstrate task extraction logic."""
    print("🎬 GOOGLE SHEETS INTEGRATION - TASK EXTRACTION DEMO")
    print("=" * 70)
    
    # Sample AI output from meeting transcript
    ai_output = {
        "meeting_title": "Project Alpha Q1 Planning Session",
        "meeting_date": "2025-01-31",
        "attendees": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
        "summary": "Project Alpha Q1 planning session focused on technical architecture, timeline planning, and task assignments.",
        "outcomes": [
            {
                "decision": "Finalize technical architecture and database schema",
                "owner": "<PERSON>",
                "context": "Technical foundation needed before API development can begin",
                "actions": [
                    {
                        "owner": "<PERSON>",
                        "task": "Complete database schema design",
                        "deadline": "2025-02-15",
                        "priority": "HIGH"
                    },
                    {
                        "owner": "<PERSON>", 
                        "task": "Prepare integration analysis document",
                        "deadline": "2025-02-08",
                        "priority": "HIGH"
                    },
                    {
                        "owner": "<PERSON>",
                        "task": "Research and recommend performance testing tools",
                        "deadline": "2025-02-12",
                        "priority": "MEDIUM"
                    }
                ]
            },
            {
                "decision": "Implement core API endpoints",
                "owner": "<PERSON>",
                "context": "APIs needed for frontend integration and testing",
                "actions": [
                    {
                        "owner": "John Smith",
                        "task": "Develop core API endpoints",
                        "deadline": "2025-03-01",
                        "priority": "HIGH"
                    },
                    {
                        "owner": "<PERSON>",
                        "task": "Set up automated testing framework",
                        "deadline": "2025-02-25",
                        "priority": "MEDIUM"
                    },
                    {
                        "owner": "John Smith",
                        "task": "Create deployment pipeline with CI/CD",
                        "deadline": "2025-03-05",
                        "priority": "MEDIUM"
                    }
                ]
            },
            {
                "decision": "Design user interface and coordinate stakeholder review",
                "owner": "Lisa Rodriguez",
                "context": "UI/UX design needed for development and stakeholder approval",
                "actions": [
                    {
                        "owner": "Lisa Rodriguez",
                        "task": "Prepare stakeholder presentation deck",
                        "deadline": "2025-02-18",
                        "priority": "HIGH"
                    },
                    {
                        "owner": "Lisa Rodriguez",
                        "task": "Coordinate UAT scheduling with business team",
                        "deadline": "2025-02-05",
                        "priority": "MEDIUM"
                    }
                ]
            }
        ]
    }
    
    print(f"📊 Meeting: {ai_output['meeting_title']}")
    print(f"📅 Date: {ai_output['meeting_date']}")
    print(f"👥 Attendees: {', '.join(ai_output['attendees'])}")
    print(f"📋 Outcomes: {len(ai_output['outcomes'])}")
    
    # Extract tasks using the same logic as GoogleSheetsService
    def extract_tasks_from_ai_output(ai_output):
        """Extract task data from AI summarizer JSON output."""
        tasks = []
        meeting_title = ai_output.get('meeting_title', 'Unknown Meeting')
        date_added = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        outcomes = ai_output.get('outcomes', [])
        for outcome in outcomes:
            decision = outcome.get('decision', 'No decision')
            context = outcome.get('context', 'No context')
            owner = outcome.get('owner', 'Unknown')
            
            actions = outcome.get('actions', [])
            for action in actions:
                task_row = [
                    action.get('task', 'No task description'),
                    action.get('priority', 'MEDIUM'),
                    action.get('owner', owner),
                    action.get('deadline', 'Not specified'),
                    meeting_title,
                    date_added,
                    'Open',
                    owner,
                    f"{decision} | {context}"[:200]  # Limit context length
                ]
                tasks.append(task_row)
        
        return tasks
    
    # Extract tasks
    tasks = extract_tasks_from_ai_output(ai_output)
    
    print(f"\n✅ Extracted {len(tasks)} tasks from AI output")
    
    # Display as spreadsheet
    print("\n📊 GOOGLE SHEETS PREVIEW:")
    print("=" * 140)
    
    # Headers
    headers = [
        "Task Description", "Priority", "Assigned To", "Deadline", 
        "Meeting Title", "Date Added", "Status", "Owner", "Context"
    ]
    
    # Print headers
    header_row = " | ".join(f"{h:<18}" for h in headers)
    print(header_row)
    print("=" * 140)
    
    # Print tasks
    for i, task in enumerate(tasks, 1):
        # Truncate long fields for display
        display_task = [
            task[0][:18] + "..." if len(task[0]) > 18 else task[0],  # Task
            task[1][:8],  # Priority
            task[2][:15] + "..." if len(task[2]) > 15 else task[2],  # Assigned
            task[3][:10],  # Deadline
            task[4][:15] + "..." if len(task[4]) > 15 else task[4],  # Meeting
            task[5][:16],  # Date Added
            task[6][:8],  # Status
            task[7][:15] + "..." if len(task[7]) > 15 else task[7],  # Owner
            task[8][:18] + "..." if len(task[8]) > 18 else task[8]   # Context
        ]
        
        task_row = " | ".join(f"{str(field):<18}" for field in display_task)
        print(f"{i:2d}. {task_row}")
    
    print("=" * 140)
    
    return tasks


def demo_workflow_steps():
    """Show the 7-step workflow."""
    print("\n🚀 7-STEP MEETING INTELLIGENCE WORKFLOW")
    print("=" * 70)
    
    workflow_steps = [
        ("1. 🔍 Identify Meeting & Transcript", "Find recent meetings and locate transcript files"),
        ("2. 🤖 Summarize Transcript (AI)", "AI analyzes transcript and extracts key information"),
        ("3. 📄 Generate JSON & HTML Summaries", "Create structured summaries in multiple formats"),
        ("4. 📧 Email Summaries to Attendees", "Send meeting summaries to all participants"),
        ("5. 💾 Store Summaries in Google Drive", "Save summaries to shared Drive folder"),
        ("6. 📅 Attach Summary to Calendar Event", "Link summary to original calendar meeting"),
        ("7. 📊 Extract Tasks to Google Sheets", "NEW: Create task management spreadsheet")
    ]
    
    for step, description in workflow_steps:
        if "NEW" in step:
            print(f"   ✨ {step}")
            print(f"      {description}")
        else:
            print(f"   ✅ {step}")
            print(f"      {description}")
        print()


def demo_authentication_info():
    """Show authentication information."""
    print("🔐 AUTHENTICATION SETUP")
    print("=" * 70)
    
    print("📋 Required OAuth2 Scopes:")
    scopes = [
        ("gmail.send", "Send meeting summary emails"),
        ("gmail.readonly", "Read email for meeting context"),
        ("calendar.readonly", "Find recent meetings"),
        ("calendar.events", "Attach summaries to calendar"),
        ("drive.readonly", "Read transcript files"),
        ("drive.file", "Store summary documents"),
        ("spreadsheets", "NEW: Create and manage task sheets")
    ]
    
    for scope, description in scopes:
        if "NEW" in description:
            print(f"   ✨ {scope:<20} - {description}")
        else:
            print(f"   ✅ {scope:<20} - {description}")
    
    print("\n🔑 To Re-authenticate with Sheets Access:")
    print("   1. Run: python reauth_with_sheets.py")
    print("   2. Complete OAuth2 flow in browser")
    print("   3. Grant access to Google Sheets when prompted")
    print("   4. Token will be saved for future use")


def main():
    """Run the demonstration."""
    # Demo 1: Task extraction
    tasks = demo_task_extraction()
    
    # Demo 2: Workflow steps
    demo_workflow_steps()
    
    # Demo 3: Authentication info
    demo_authentication_info()
    
    # Summary
    print("\n" + "=" * 70)
    print("🎉 GOOGLE SHEETS INTEGRATION SUMMARY")
    print("=" * 70)
    
    print("✅ Integration Status: COMPLETE")
    print("✅ Code Implementation: READY")
    print("✅ Task Extraction: WORKING")
    print("✅ Workflow Extension: 6 → 7 steps")
    
    print(f"\n📊 Demo Results:")
    print(f"   📋 Extracted {len(tasks)} tasks from sample meeting")
    print(f"   🎯 Tasks include priorities, deadlines, and assignments")
    print(f"   📊 Ready for Google Sheets integration")
    
    print("\n🚀 What Happens Next:")
    print("   1. Re-authenticate to include Sheets scope")
    print("   2. Run your Meeting Intelligence Agent")
    print("   3. Agent automatically creates task spreadsheets")
    print("   4. Team gets organized, actionable task lists")
    
    print("\n🎯 Ready to Activate:")
    print("   python reauth_with_sheets.py  # Re-authenticate")
    print("   python run_agent.py          # Run complete workflow")


if __name__ == "__main__":
    main()
