# db.py

"""
Database Configuration for Meeting Intelligence Agent
Configured for Google Cloud SQL (MySQL 8.0)
"""

import os
import sys
import logging
from typing import Generator
from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import QueuePool
# Ensure parent path is accessible
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import settings
from src.configuration.config import DB_URL, DEBUG, LOG_FORMAT, LOG_DATE_FORMAT, LOG_FILE_PATH, LOG_LEVEL

# ---------------- Configure Logging ----------------
numeric_level = getattr(logging, LOG_LEVEL.upper(), logging.INFO)
os.makedirs(os.path.dirname(LOG_FILE_PATH), exist_ok=True)

logging.basicConfig(
    level=numeric_level,
    format=LOG_FORMAT,
    datefmt=LOG_DATE_FORMAT,
    handlers=[
        logging.FileHandler(LOG_FILE_PATH),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ---------------- SQLAlchemy Setup ----------------
engine = create_engine(
    DB_URL,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=DEBUG
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()
metadata = MetaData()

# ---------------- DB Utilities ----------------
def get_db() -> Generator:
    """Database session generator for dependency injection"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """Create all database tables"""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("✅ Database tables created successfully")
    except Exception as e:
        logger.error(f"❌ Error creating database tables: {e}")
        raise

def drop_tables():
    """Drop all database tables (use with caution!)"""
    try:
        Base.metadata.drop_all(bind=engine)
        logger.warning("⚠️ All database tables dropped")
    except Exception as e:
        logger.error(f"❌ Error dropping database tables: {e}")
        raise

def test_connection() -> bool:
    """Test database connection"""
    try:
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
            logger.info("✅ Database connection successful")
            return True
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False

# ---------------- Manual Test Mode ----------------
if __name__ == "__main__":
    print("🔍 Testing Database Configuration")
    print("=" * 40)
    print(f"DB_URL: {DB_URL}")
    print("=" * 40)

    if test_connection():
        print("✅ Connection is valid")
    else:
        print("❌ Connection failed")
