"""Test script for Google Sheets integration."""

import os
import sys
import json
import logging
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.services.utility.google_auth import GoogleAuthenticator
from src.services.google_sheets_service import GoogleSheetsService
from src.tools.langchain_sheets_tool import SheetsToolLangChain

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_sheets_authentication():
    """Test Google Sheets authentication."""
    print("🔐 Testing Google Sheets Authentication...")
    
    try:
        # Initialize authenticator
        auth = GoogleAuthenticator()
        
        # Test authentication
        if auth.test_authentication():
            print("✅ Google Sheets authentication successful!")
            return auth
        else:
            print("❌ Google Sheets authentication failed!")
            return None
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None


def test_sheets_service(auth):
    """Test Google Sheets service functionality."""
    print("\n📊 Testing Google Sheets Service...")
    
    try:
        # Initialize service
        sheets_service = GoogleSheetsService(auth)
        
        # Test connection
        if sheets_service.test_connection():
            print("✅ Sheets service connection successful!")
            return sheets_service
        else:
            print("❌ Sheets service connection failed!")
            return None
            
    except Exception as e:
        print(f"❌ Sheets service error: {e}")
        return None


def test_task_extraction():
    """Test task extraction from AI output."""
    print("\n📋 Testing Task Extraction...")
    
    # Sample AI output (similar to what the summarizer produces)
    sample_ai_output = {
        "meeting_title": "Project Planning Meeting",
        "meeting_date": "2025-01-31",
        "outcomes": [
            {
                "decision": "Implement new feature by Q2",
                "owner": "John Smith",
                "context": "Team agreed on timeline and resource allocation",
                "actions": [
                    {
                        "owner": "John Smith",
                        "task": "Create technical specification document",
                        "deadline": "2025-02-15",
                        "priority": "HIGH"
                    },
                    {
                        "owner": "Sarah Johnson",
                        "task": "Review and approve budget allocation",
                        "deadline": "2025-02-10",
                        "priority": "MEDIUM"
                    }
                ]
            },
            {
                "decision": "Schedule weekly check-ins",
                "owner": "Team Lead",
                "context": "Regular progress monitoring needed",
                "actions": [
                    {
                        "owner": "Team Lead",
                        "task": "Set up recurring meeting series",
                        "deadline": "2025-02-05",
                        "priority": "LOW"
                    }
                ]
            }
        ]
    }
    
    try:
        # Initialize service with dummy auth for testing extraction only
        auth = GoogleAuthenticator()
        sheets_service = GoogleSheetsService(auth)
        
        # Test task extraction
        tasks = sheets_service.extract_tasks_from_ai_output(sample_ai_output)
        
        print(f"✅ Extracted {len(tasks)} tasks from AI output:")
        for i, task in enumerate(tasks, 1):
            print(f"   Task {i}: {task[0][:50]}... | Priority: {task[1]} | Assigned: {task[2]}")
        
        return tasks
        
    except Exception as e:
        print(f"❌ Task extraction error: {e}")
        return []


def test_langchain_tool(auth):
    """Test LangChain Sheets tool."""
    print("\n🔧 Testing LangChain Sheets Tool...")
    
    try:
        # Initialize tool
        sheets_tool = SheetsToolLangChain(auth=auth)
        
        # Test tool creation (without actually creating a sheet)
        print("✅ LangChain Sheets tool initialized successfully!")
        print(f"   Tool name: {sheets_tool.name}")
        print(f"   Tool description: {sheets_tool.description[:100]}...")
        
        return sheets_tool
        
    except Exception as e:
        print(f"❌ LangChain tool error: {e}")
        return None


def main():
    """Run all tests."""
    print("🚀 Starting Google Sheets Integration Tests")
    print("=" * 50)
    
    # Test 1: Authentication
    auth = test_sheets_authentication()
    if not auth:
        print("\n❌ Cannot proceed without authentication")
        return
    
    # Test 2: Sheets Service
    sheets_service = test_sheets_service(auth)
    if not sheets_service:
        print("\n❌ Cannot proceed without Sheets service")
        return
    
    # Test 3: Task Extraction
    tasks = test_task_extraction()
    if not tasks:
        print("\n❌ Task extraction failed")
        return
    
    # Test 4: LangChain Tool
    sheets_tool = test_langchain_tool(auth)
    if not sheets_tool:
        print("\n❌ LangChain tool initialization failed")
        return
    
    print("\n" + "=" * 50)
    print("🎉 All Google Sheets Integration Tests Passed!")
    print("\n📋 Summary:")
    print(f"   ✅ Authentication: Working")
    print(f"   ✅ Sheets Service: Working")
    print(f"   ✅ Task Extraction: {len(tasks)} tasks extracted")
    print(f"   ✅ LangChain Tool: Working")
    
    print("\n🔄 Next Steps:")
    print("   1. Re-authenticate to include Sheets scope")
    print("   2. Test creating a real spreadsheet")
    print("   3. Test writing tasks to the spreadsheet")
    print("   4. Test the complete 7-step workflow")


if __name__ == "__main__":
    main()
