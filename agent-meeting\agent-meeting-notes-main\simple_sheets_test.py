"""Simple test of centralized sheets functionality."""

import os
import sys
import json

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

print("🚀 Starting Simple Centralized Sheets Test")
print("=" * 50)

try:
    print("📦 Importing modules...")
    from src.services.utility.google_auth import GoogleAuthenticator
    from src.services.user_sheet_manager import UserSheetManager
    print("✅ Modules imported successfully")
    
    print("\n🔐 Initializing authentication...")
    auth = GoogleAuthenticator()
    print(f"✅ Authentication initialized")
    
    user_email = auth.get_user_email()
    print(f"👤 User email: {user_email}")
    
    print("\n📊 Initializing UserSheetManager...")
    sheet_manager = UserSheetManager(auth)
    print("✅ UserSheetManager initialized")
    
    print("\n📋 Creating/ensuring meeting_task sheet...")
    sheet_id = sheet_manager.create_meeting_task_sheet_if_not_exists(user_email)
    print(f"📊 Sheet ID: {sheet_id}")
    
    if sheet_id:
        print("✅ Centralized meeting_task sheet is ready!")
        print(f"🔗 Sheet URL: https://docs.google.com/spreadsheets/d/{sheet_id}")
        
        # Test task writing
        print("\n💾 Testing task writing...")
        sample_tasks = [
            {
                "task": "Test centralized sheet functionality",
                "priority": "HIGH",
                "owner": "Test User",
                "deadline": "2024-01-20",
                "meeting_title": "Simple Test Meeting",
                "context": "Testing the centralized approach"
            }
        ]
        
        success = sheet_manager.append_tasks_to_sheet(sheet_id, sample_tasks)
        if success:
            print("✅ Task written successfully!")
            print("🎉 Centralized sheets integration is working!")
        else:
            print("❌ Failed to write task")
    else:
        print("❌ Failed to create/ensure sheet")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("🏁 Test completed")
