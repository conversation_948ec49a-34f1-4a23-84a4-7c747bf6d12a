"""Google Calendar service utilities."""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field

from googleapiclient.errors import HttpError
from .google_auth import GoogleAuthenticator

logger = logging.getLogger(__name__)


@dataclass
class CalendarEvent:
    """Represents a Google Calendar event."""
    id: str
    summary: str
    description: str
    start_time: datetime
    end_time: datetime
    location: str = ""
    attendees: List[str] = field(default_factory=list)
    organizer: str = ""
    
    # __post_init__ no longer needed


class GoogleCalendarService:
    """Service for interacting with Google Calendar API."""
    
    def __init__(self, authenticator: GoogleAuthenticator):
        """
        Initialize Calendar service.
        
        Args:
            authenticator: Google authenticator instance
        """
        self.authenticator = authenticator
        self.service = authenticator.get_calendar_service()
        
        if not self.service:
            logger.error("Failed to initialize Google Calendar service")
    
    def get_events_in_range(
        self, 
        start_time: datetime, 
        end_time: datetime,
        calendar_id: str = 'primary'
    ) -> List[CalendarEvent]:
        """
        Get calendar events within a time range.
        
        Args:
            start_time: Start of time range
            end_time: End of time range
            calendar_id: Calendar ID (default: primary)
            
        Returns:
            List of CalendarEvent objects
        """
        if not self.service:
            logger.error("Calendar service not available")
            return []
        
        try:
            # Format times for API
            start_time_str = start_time.isoformat() + 'Z'
            end_time_str = end_time.isoformat() + 'Z'
            
            # Call Calendar API
            events_result = self.service.events().list(
                calendarId=calendar_id,
                timeMin=start_time_str,
                timeMax=end_time_str,
                singleEvents=True,
                orderBy='startTime'
            ).execute()
            
            events = events_result.get('items', [])
            
            # Convert to CalendarEvent objects
            calendar_events = []
            for event in events:
                try:
                    calendar_event = self._convert_to_calendar_event(event)
                    if calendar_event:
                        calendar_events.append(calendar_event)
                except Exception as e:
                    logger.warning(f"Failed to convert event {event.get('id', 'unknown')}: {e}")
            
            logger.info(f"Retrieved {len(calendar_events)} events from {start_time} to {end_time}")
            return calendar_events
            
        except HttpError as e:
            logger.error(f"Calendar API error: {e}")
            return []
        except Exception as e:
            logger.error(f"Error retrieving calendar events: {e}")
            return []
    
    def find_closest_event(
        self, 
        target_time: datetime, 
        search_window_hours: int = 2
    ) -> Optional[CalendarEvent]:
        """
        Find the closest calendar event to a target time.
        
        Args:
            target_time: Target datetime to search around
            search_window_hours: Hours to search before and after target time
            
        Returns:
            Closest CalendarEvent or None if no events found
        """
        # Define search range
        start_time = target_time - timedelta(hours=search_window_hours)
        end_time = target_time + timedelta(hours=search_window_hours)
        
        # Get events in range
        events = self.get_events_in_range(start_time, end_time)
        
        if not events:
            logger.info(f"No events found within {search_window_hours} hours of {target_time}")
            return None
        
        # Find closest event
        closest_event = None
        min_time_diff = None
        
        for event in events:
            # Calculate time difference from event start to target time
            time_diff = abs((event.start_time - target_time).total_seconds())
            
            if min_time_diff is None or time_diff < min_time_diff:
                min_time_diff = time_diff
                closest_event = event
        
        if closest_event:
            time_diff_minutes = min_time_diff / 60
            logger.info(f"Found closest event: '{closest_event.summary}' "
                       f"({time_diff_minutes:.1f} minutes from target time)")
        
        return closest_event
    
    def _convert_to_calendar_event(self, event_data: Dict[str, Any]) -> Optional[CalendarEvent]:
        """
        Convert Google Calendar API event data to CalendarEvent object.
        
        Args:
            event_data: Raw event data from Google Calendar API
            
        Returns:
            CalendarEvent object or None if conversion fails
        """
        try:
            # Extract basic information
            event_id = event_data.get('id', '')
            summary = event_data.get('summary', 'No Title')
            description = event_data.get('description', '')
            location = event_data.get('location', '')
            
            # Extract start and end times
            start_data = event_data.get('start', {})
            end_data = event_data.get('end', {})
            
            # Handle different time formats
            start_time = self._parse_event_time(start_data)
            end_time = self._parse_event_time(end_data)

            # Ensure start_time and end_time are valid datetimes
            if not isinstance(start_time, datetime) or not isinstance(end_time, datetime):
                logger.warning(f"Could not parse times for event {event_id}")
                return None
            
            # Extract attendees
            attendees = []
            for attendee in event_data.get('attendees', []):
                email = attendee.get('email', '')
                if email:
                    attendees.append(email)
            
            # Extract organizer
            organizer_data = event_data.get('organizer', {})
            organizer = organizer_data.get('email', '')
            
            return CalendarEvent(
                id=event_id,
                summary=summary,
                description=description,
                start_time=start_time,
                end_time=end_time,
                location=location,
                attendees=attendees,
                organizer=organizer
            )
            
        except Exception as e:
            logger.error(f"Error converting event data: {e}")
            return None
    
    def _parse_event_time(self, time_data: Dict[str, Any]) -> Optional[datetime]:
        """
        Parse event time from Google Calendar API format.
        
        Args:
            time_data: Time data from Calendar API
            
        Returns:
            Parsed datetime or None if parsing fails
        """
        try:
            # Check for dateTime (specific time)
            if 'dateTime' in time_data:
                time_str = time_data['dateTime']
                # Remove timezone info for simplicity (can be enhanced later)
                if '+' in time_str:
                    time_str = time_str.split('+')[0]
                elif 'Z' in time_str:
                    time_str = time_str.replace('Z', '')
                
                return datetime.fromisoformat(time_str)
            
            # Check for date (all-day event)
            elif 'date' in time_data:
                date_str = time_data['date']
                return datetime.fromisoformat(date_str + 'T00:00:00')
            
            else:
                logger.warning("No valid time format found in event data")
                return None
                
        except Exception as e:
            logger.error(f"Error parsing event time: {e}")
            return None
    
    def test_connection(self) -> bool:
        """
        Test Calendar service connection.
        
        Returns:
            True if connection is working, False otherwise
        """
        if not self.service:
            return False
        
        try:
            # Try to list calendars
            calendar_list = self.service.calendarList().list().execute()
            calendars = calendar_list.get('items', [])
            logger.info(f"Calendar connection test passed. Found {len(calendars)} calendars.")
            return True
            
        except Exception as e:
            logger.error(f"Calendar connection test failed: {e}")
            return False

    def add_attachment_to_event(self, event_id: str, file_id: str, title: str, mime_type: str, web_view_link: str = None, calendar_id: str = 'primary') -> bool:
        """
        Add a Google Drive file as an attachment to a calendar event.
        Args:
            event_id: The ID of the calendar event
            file_id: The Google Drive file ID
            title: The display name of the attachment
            mime_type: The MIME type of the file
            web_view_link: Optional webViewLink for the file
            calendar_id: Calendar ID (default: 'primary')
        Returns:
            True if successful, False otherwise
        """
        if not self.service:
            logger.error("Calendar service not available for adding attachment.")
            return False
        try:
            # Ensure all parameters are strings
            event_id = str(event_id) if event_id is not None else ''
            file_id = str(file_id) if file_id is not None else ''
            title = str(title) if title is not None else ''
            mime_type = str(mime_type) if mime_type is not None else ''
            calendar_id = str(calendar_id) if calendar_id is not None else 'primary'
            web_view_link = str(web_view_link) if web_view_link is not None else f'https://drive.google.com/open?id={file_id}'
            attachment = {
                'fileId': file_id,
                'title': title,
                'mimeType': mime_type,
                'iconLink': None,
                'fileUrl': web_view_link or f'https://drive.google.com/open?id={file_id}'
            }
            event_patch = {
                'attachments': [attachment]
            }
            updated_event = self.service.events().patch(
                calendarId=calendar_id,
                eventId=event_id,
                body=event_patch,
                supportsAttachments=True
            ).execute()
            logger.info(f"Added attachment to event {event_id}: file {file_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to add attachment to event {event_id}: {e}")
            return False
