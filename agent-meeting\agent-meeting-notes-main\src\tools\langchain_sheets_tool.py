"""Lang<PERSON><PERSON>n Google Sheets Tool for task management integration."""

import json
import logging
from typing import Dict, Any, Optional, Type

try:
    from pydantic import BaseModel, Field
    from langchain_core.tools import BaseTool
    from langchain_core.callbacks import CallbackManagerForToolRun
    LANGCHAIN_AVAILABLE = True
except ImportError as e:
    logging.warning(f"LangChain/Pydantic not available: {e}")
    LANGCHAIN_AVAILABLE = False
    # Fallback classes
    BaseModel = object
    Field = lambda **kwargs: None
    BaseTool = object
    CallbackManagerForToolRun = object

from src.services.utility.google_auth import GoogleAuthenticator
from src.services.google_sheets_service import GoogleSheetsService

# Import tool configs and categories
# from src.constants.app import AVAILABLE_TOOLS, TOOL_CATEGORIES  # Available if needed

logger = logging.getLogger(__name__)


class SheetsToolInput(BaseModel):
    """Input schema for Sheets tool."""
    action: str = Field(description="Action to perform: 'create_sheet', 'write_tasks', 'read_tasks', 'update_task'")
    sheet_id: Optional[str] = Field(None, description="Spreadsheet ID (required for most actions)")
    user_id: Optional[str] = Field(None, description="User identifier for sheet creation")
    ai_output: Optional[Dict[str, Any]] = Field(None, description="AI summarizer output containing tasks")
    task_data: Optional[Dict[str, Any]] = Field(None, description="Individual task data for updates")


class SheetsToolOutput(BaseModel):
    """Output schema for Sheets tool."""
    status: str = Field(description="Status of the operation")
    message: str = Field(description="Human-readable message")
    sheet_id: Optional[str] = Field(None, description="Spreadsheet ID")
    sheet_url: Optional[str] = Field(None, description="Web URL to the spreadsheet")
    tasks_written: Optional[int] = Field(None, description="Number of tasks written")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional data")


class SheetsToolError(Exception):
    """Custom exception for Sheets tool errors."""
    pass


class SheetsToolConfig:
    """Configuration for the Sheets tool."""
    
    # Tool metadata
    TOOL_NAME = "sheets_tool"
    TOOL_DESCRIPTION = """
    Google Sheets integration tool for meeting task management.
    
    Capabilities:
    - Create new task spreadsheets for users
    - Extract tasks from AI meeting summaries
    - Write task data to Google Sheets
    - Read existing task data
    - Update task status and information
    
    Actions:
    - create_sheet: Create a new task spreadsheet
    - write_tasks: Extract and write tasks from AI output
    - read_tasks: Read existing tasks from a sheet
    - update_task: Update specific task information
    """
    TOOL_CATEGORY = "productivity"


class SheetsToolRegistry:
    """Registry for managing user spreadsheets."""
    
    def __init__(self):
        self._user_sheets = {}  # user_id -> sheet_id mapping
    
    def register_sheet(self, user_id: str, sheet_id: str):
        """Register a sheet for a user."""
        self._user_sheets[user_id] = sheet_id
        logger.info(f"Registered sheet {sheet_id} for user {user_id}")
    
    def get_sheet_id(self, user_id: str) -> Optional[str]:
        """Get the sheet ID for a user."""
        return self._user_sheets.get(user_id)
    
    def has_sheet(self, user_id: str) -> bool:
        """Check if user has a registered sheet."""
        return user_id in self._user_sheets


# Global registry instance
# _sheet_registry = SheetsToolRegistry()  # Available if needed


class SheetsToolHandler:
    """Handler for Sheets tool operations."""
    
    def __init__(self, sheets_service: GoogleSheetsService):
        self.sheets_service = sheets_service
    
    def create_sheet(self, user_id: str, sheet_name: str = "meeting_task") -> SheetsToolOutput:
        """Ensure user has a centralized meeting_task spreadsheet."""
        try:
            # Use centralized approach - ensure user has meeting_task sheet
            sheet_id = self.sheets_service.ensure_user_meeting_task_sheet(user_id)

            if sheet_id:
                # Get sheet info
                sheet_info = self.sheets_service.get_user_sheet_info(user_id)

                return SheetsToolOutput(
                    status="ready",
                    message=f"Meeting task sheet ready for {user_id}",
                    sheet_id=sheet_id,
                    sheet_url=sheet_info.get('sheet_url') if sheet_info else f"https://docs.google.com/spreadsheets/d/{sheet_id}"
                )
            else:
                raise SheetsToolError("Failed to ensure meeting_task sheet exists")

        except Exception as e:
            logger.error(f"Failed to ensure meeting_task sheet for {user_id}: {e}")
            return SheetsToolOutput(
                status="error",
                message=f"Failed to ensure meeting_task sheet: {str(e)}"
            )
    
    def write_tasks(self, user_id: str, ai_output: Dict[str, Any]) -> SheetsToolOutput:
        """Write tasks from AI output to user's centralized meeting_task sheet."""
        try:
            # Use centralized approach - write to user's meeting_task sheet
            success = self.sheets_service.write_tasks_from_ai_summary(ai_output, user_id)

            if success:
                # Get sheet info and count tasks
                sheet_info = self.sheets_service.get_user_sheet_info(user_id)
                tasks = self.sheets_service.user_sheet_manager.extract_tasks_from_summary(ai_output)
                task_count = len(tasks)

                return SheetsToolOutput(
                    status="success",
                    message=f"Successfully wrote {task_count} tasks to meeting_task sheet",
                    sheet_id=sheet_info.get('sheet_id') if sheet_info else None,
                    sheet_url=sheet_info.get('sheet_url') if sheet_info else None,
                    tasks_written=task_count
                )
            else:
                raise SheetsToolError("Failed to write tasks to spreadsheet")
                
        except Exception as e:
            logger.error(f"Failed to write tasks to sheet: {e}")
            return SheetsToolOutput(
                status="error",
                message=f"Failed to write tasks: {str(e)}"
            )
    
    def read_tasks(self, sheet_id: str, range_name: str = "Tasks!A:I") -> SheetsToolOutput:
        """Read tasks from the spreadsheet."""
        try:
            # Read data from sheet
            data = self.sheets_service.read_range(sheet_id, range_name)
            
            if data:
                # Parse tasks (skip header row)
                headers = data[0] if data else []
                tasks = data[1:] if len(data) > 1 else []
                
                return SheetsToolOutput(
                    status="success",
                    message=f"Read {len(tasks)} tasks from spreadsheet",
                    sheet_id=sheet_id,
                    sheet_url=self.sheets_service.get_sheet_url(sheet_id),
                    data={
                        "headers": headers,
                        "tasks": tasks,
                        "task_count": len(tasks)
                    }
                )
            else:
                return SheetsToolOutput(
                    status="empty",
                    message="No tasks found in spreadsheet",
                    sheet_id=sheet_id,
                    sheet_url=self.sheets_service.get_sheet_url(sheet_id),
                    data={"task_count": 0}
                )
                
        except Exception as e:
            logger.error(f"Failed to read tasks from sheet {sheet_id}: {e}")
            return SheetsToolOutput(
                status="error",
                message=f"Failed to read tasks: {str(e)}"
            )
    
    def update_task(self, sheet_id: str, cell_range: str, value: str) -> SheetsToolOutput:
        """Update a specific task cell."""
        try:
            success = self.sheets_service.update_cell(sheet_id, cell_range, value)
            
            if success:
                return SheetsToolOutput(
                    status="success",
                    message=f"Updated cell {cell_range} with value: {value}",
                    sheet_id=sheet_id,
                    sheet_url=self.sheets_service.get_sheet_url(sheet_id)
                )
            else:
                raise SheetsToolError("Failed to update cell")
                
        except Exception as e:
            logger.error(f"Failed to update cell {cell_range}: {e}")
            return SheetsToolOutput(
                status="error",
                message=f"Failed to update cell: {str(e)}"
            )


class SheetsToolLangChain(BaseTool):
    """
    LangChain tool for Google Sheets task management.
    
    This tool integrates with the Meeting Intelligence Agent to:
    - Create task spreadsheets for authenticated users
    - Extract tasks from AI meeting summaries
    - Write structured task data to Google Sheets
    - Manage task status and updates
    """
    
    name: str = SheetsToolConfig.TOOL_NAME
    description: str = SheetsToolConfig.TOOL_DESCRIPTION
    category: str = SheetsToolConfig.TOOL_CATEGORY
    args_schema: Type[BaseModel] = SheetsToolInput
    
    # Declare services as class variables to avoid Pydantic validation issues
    auth: Optional[GoogleAuthenticator] = None
    sheets_service: Optional[GoogleSheetsService] = None
    handler: Optional[SheetsToolHandler] = None
    
    def __init__(self, auth: GoogleAuthenticator, **kwargs):
        super().__init__(**kwargs)
        self.auth = auth
        self.sheets_service = GoogleSheetsService(auth)
        self.handler = SheetsToolHandler(self.sheets_service)
    
    def _run(
        self,
        action: str,
        sheet_id: Optional[str] = None,
        user_id: Optional[str] = None,
        ai_output: Optional[Dict[str, Any]] = None,
        task_data: Optional[Dict[str, Any]] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute Sheets operations."""
        try:
            logger.info(f"Executing Sheets tool action: {action}")
            
            if action == "create_sheet":
                if not user_id:
                    raise SheetsToolError("user_id is required for create_sheet action")
                result = self.handler.create_sheet(user_id)
                
            elif action == "write_tasks":
                if not user_id or not ai_output:
                    raise SheetsToolError("user_id and ai_output are required for write_tasks action")
                result = self.handler.write_tasks(user_id, ai_output)
                
            elif action == "read_tasks":
                if not sheet_id:
                    raise SheetsToolError("sheet_id is required for read_tasks action")
                result = self.handler.read_tasks(sheet_id)
                
            elif action == "update_task":
                if not sheet_id or not task_data:
                    raise SheetsToolError("sheet_id and task_data are required for update_task action")
                
                cell_range = task_data.get("cell_range")
                value = task_data.get("value")
                
                if not cell_range or value is None:
                    raise SheetsToolError("task_data must contain 'cell_range' and 'value'")
                
                result = self.handler.update_task(sheet_id, cell_range, str(value))
                
            else:
                raise SheetsToolError(f"Unknown action: {action}")
            
            return json.dumps(result.dict(), indent=2)
            
        except Exception as e:
            logger.error(f"Sheets tool error: {e}")
            error_result = SheetsToolOutput(
                status="error",
                message=f"Sheets tool error: {str(e)}"
            )
            return json.dumps(error_result.dict(), indent=2)
    
    async def _arun(
        self,
        action: str,
        sheet_id: Optional[str] = None,
        user_id: Optional[str] = None,
        ai_output: Optional[Dict[str, Any]] = None,
        task_data: Optional[Dict[str, Any]] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute Sheets operations asynchronously."""
        return self._run(action, sheet_id, user_id, ai_output, task_data, run_manager)
