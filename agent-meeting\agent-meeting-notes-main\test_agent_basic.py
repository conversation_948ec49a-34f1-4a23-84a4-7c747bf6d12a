#!/usr/bin/env python3
"""
Basic test script to verify the agent is working correctly.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all core modules can be imported."""
    print("🧪 Testing Core Imports...")
    
    try:
        # Test configuration
        from src.configuration.config import APP_NAME, APP_VERSION
        print(f"✅ Configuration loaded: {APP_NAME} v{APP_VERSION}")
        
        # Test constants
        from src.constants.app import AVAILABLE_TOOLS
        print(f"✅ Available tools: {len(AVAILABLE_TOOLS)} tools loaded")
        
        # Test agent
        from src.agents.langchain_meeting_agent import LangChainMeetingAgent
        print("✅ LangChain Meeting Agent imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_agent_initialization():
    """Test agent initialization."""
    print("\n🤖 Testing Agent Initialization...")
    
    try:
        from src.agents.langchain_meeting_agent import LangChainMeetingAgent
        
        # Initialize agent
        agent = LangChainMeetingAgent()
        print("✅ Agent initialized successfully")
        
        # Check if tools are loaded
        if hasattr(agent, 'tools') and agent.tools:
            print(f"✅ Tools loaded: {len(agent.tools)} tools available")
            for tool in agent.tools:
                print(f"   - {tool.name}: {tool.description[:50]}...")
        else:
            print("⚠️  No tools loaded (this may be expected if auth is not configured)")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent initialization failed: {e}")
        return False

def test_basic_chat():
    """Test basic chat functionality."""
    print("\n💬 Testing Basic Chat...")
    
    try:
        from src.agents.langchain_meeting_agent import LangChainMeetingAgent
        
        agent = LangChainMeetingAgent()
        
        # Test a simple query
        response = agent.chat("Hello, what can you do?")
        print(f"✅ Chat response received: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Chat test failed: {e}")
        return False

async def test_workflow():
    """Test the main workflow function."""
    print("\n⚙️  Testing Workflow Function...")
    
    try:
        from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow
        
        # Run the workflow
        result = await run_autonomous_meeting_workflow()
        print("✅ Workflow executed successfully")
        print(f"   Status: {result.get('status', 'unknown')}")
        print(f"   Message: {result.get('agent_output', 'No output')[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Meeting Intelligence Agent - Basic Tests")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Agent Initialization", test_agent_initialization),
        ("Basic Chat", test_basic_chat),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Test async workflow
    print("\n⚙️  Testing Async Workflow...")
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        workflow_result = loop.run_until_complete(test_workflow())
        results.append(("Workflow Test", workflow_result))
        loop.close()
    except Exception as e:
        print(f"❌ Workflow test crashed: {e}")
        results.append(("Workflow Test", False))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The agent is working correctly.")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
