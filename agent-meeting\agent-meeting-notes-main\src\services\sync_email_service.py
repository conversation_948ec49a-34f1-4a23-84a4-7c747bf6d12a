
import asyncio
from typing import Dict, Any, List, Optional

class SyncEmailService:
    """Synchronous wrapper for EmailService."""
    
    def __init__(self, email_service):
        self.email_service = email_service
    
    def send_email(self, to_email: str, subject: str, body: str,
                   html_body: Optional[str] = None,
                   attachments: Optional[List[str]] = None) -> Dict[str, Any]:
        """Send email synchronously."""
        async def _send():
            return await self.email_service.send_email(
                to_email=to_email,
                subject=subject,
                body=body,
                html_body=html_body,
                attachments=attachments
            )
        
        return asyncio.run(_send())
