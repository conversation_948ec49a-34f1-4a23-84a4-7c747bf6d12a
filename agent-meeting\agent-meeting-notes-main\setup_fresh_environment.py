"""
Fresh Environment Setup Script for Meeting Intelligence Agent
This script sets up a clean environment and performs fresh OAuth authentication.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(command, description):
    """Run a command and handle errors."""
    logger.info(f"🔄 {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"✅ {description} - Success")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} - Failed")
        logger.error(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    logger.info("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        logger.info(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        logger.error(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible. Need Python 3.8+")
        return False

def install_dependencies():
    """Install all required dependencies."""
    logger.info("📦 Installing dependencies...")
    
    # Core dependencies
    dependencies = [
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0",
        "python-multipart==0.0.6",
        "jinja2==3.1.2",
        "python-dotenv==1.0.0",
        "pydantic==2.5.0",
        "requests==2.31.0",
        "certifi==2023.11.17",
        
        # Google APIs
        "google-auth==2.23.4",
        "google-auth-oauthlib==1.1.0",
        "google-auth-httplib2==0.1.1",
        "google-api-python-client==2.108.0",
        
        # LangChain
        "langchain==0.0.350",
        "langchain-core==0.1.0",
        "langchain-google-vertexai==0.0.6",
        
        # Database
        "sqlalchemy==2.0.23",
        "pymysql==1.1.0",
        "cryptography==41.0.7",
        
        # Utilities
        "python-dateutil==2.8.2",
        "pytz==2023.3",
        "markdown==3.5.1",
        "beautifulsoup4==4.12.2",
        "lxml==4.9.3"
    ]
    
    success = True
    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            success = False
    
    return success

def clean_old_credentials():
    """Clean old OAuth credentials and tokens."""
    logger.info("🧹 Cleaning old credentials...")
    
    files_to_remove = [
        "token.json",
        "credentials.json.backup",
        "temp_user_sheets.json"
    ]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.info(f"✅ Removed {file_path}")
            except Exception as e:
                logger.warning(f"⚠️ Could not remove {file_path}: {e}")
        else:
            logger.info(f"ℹ️ {file_path} not found (already clean)")

def verify_credentials_file():
    """Verify that credentials.json exists."""
    logger.info("🔑 Checking for credentials.json...")
    
    if os.path.exists("credentials.json"):
        logger.info("✅ credentials.json found")
        return True
    else:
        logger.error("❌ credentials.json not found!")
        logger.error("Please download your OAuth2 credentials from Google Cloud Console:")
        logger.error("1. Go to https://console.cloud.google.com/")
        logger.error("2. Select your project")
        logger.error("3. Go to APIs & Services > Credentials")
        logger.error("4. Download the OAuth2 client credentials as 'credentials.json'")
        logger.error("5. Place it in the agent-meeting-notes-main directory")
        return False

def test_imports():
    """Test that all critical imports work."""
    logger.info("🧪 Testing critical imports...")
    
    try:
        # Test Google APIs
        from google.auth.transport.requests import Request
        from google.oauth2.credentials import Credentials
        from google_auth_oauthlib.flow import InstalledAppFlow
        from googleapiclient.discovery import build
        logger.info("✅ Google APIs imports successful")
        
        # Test LangChain
        from langchain.agents import AgentExecutor
        from langchain_core.prompts import ChatPromptTemplate
        logger.info("✅ LangChain imports successful")
        
        # Test FastAPI
        from fastapi import FastAPI
        logger.info("✅ FastAPI imports successful")
        
        # Test database
        from sqlalchemy import create_engine
        logger.info("✅ SQLAlchemy imports successful")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import test failed: {e}")
        return False

def run_fresh_oauth():
    """Run fresh OAuth authentication."""
    logger.info("🔐 Starting fresh OAuth authentication...")
    
    # Add src to path
    sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
    
    try:
        from src.services.utility.google_auth import GoogleAuthenticator
        
        logger.info("🔄 Initializing Google Authenticator...")
        auth = GoogleAuthenticator()
        
        logger.info("🌐 Starting OAuth flow...")
        logger.info("📱 Your browser will open for authentication")
        logger.info("🔑 Please complete the OAuth flow in your browser")
        
        success = auth.authenticate()
        
        if success:
            logger.info("✅ OAuth authentication successful!")
            
            # Test user email retrieval
            user_email = auth.get_user_email()
            if user_email:
                logger.info(f"👤 Authenticated as: {user_email}")
            
            # Test service access
            logger.info("🧪 Testing service access...")
            gmail_service = auth.get_gmail_service()
            calendar_service = auth.get_calendar_service()
            drive_service = auth.get_drive_service()
            sheets_service = auth.get_sheets_service()
            
            services_ok = all([gmail_service, calendar_service, drive_service, sheets_service])
            
            if services_ok:
                logger.info("✅ All Google services accessible")
                return True
            else:
                logger.error("❌ Some Google services not accessible")
                return False
        else:
            logger.error("❌ OAuth authentication failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ OAuth setup failed: {e}")
        return False

def main():
    """Main setup function."""
    logger.info("🚀 Starting Fresh Environment Setup for Meeting Intelligence Agent")
    logger.info("=" * 70)
    
    # Step 1: Check Python version
    if not check_python_version():
        return False
    
    # Step 2: Install dependencies
    logger.info("\n📦 STEP 1: Installing Dependencies")
    if not install_dependencies():
        logger.error("❌ Dependency installation failed")
        return False
    
    # Step 3: Test imports
    logger.info("\n🧪 STEP 2: Testing Imports")
    if not test_imports():
        logger.error("❌ Import tests failed")
        return False
    
    # Step 4: Clean old credentials
    logger.info("\n🧹 STEP 3: Cleaning Old Credentials")
    clean_old_credentials()
    
    # Step 5: Verify credentials file
    logger.info("\n🔑 STEP 4: Verifying Credentials File")
    if not verify_credentials_file():
        return False
    
    # Step 6: Run fresh OAuth
    logger.info("\n🔐 STEP 5: Fresh OAuth Authentication")
    if not run_fresh_oauth():
        return False
    
    # Success!
    logger.info("\n" + "=" * 70)
    logger.info("🎉 SETUP COMPLETE!")
    logger.info("✅ Environment is ready")
    logger.info("✅ Dependencies installed")
    logger.info("✅ OAuth authentication successful")
    logger.info("✅ All Google services accessible")
    logger.info("\n🚀 You can now run the Meeting Intelligence Agent!")
    logger.info("💡 Next steps:")
    logger.info("   1. Run: python main.py")
    logger.info("   2. Or test the agent: python test_centralized_workflow.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        logger.error("\n❌ Setup failed. Please check the errors above.")
        sys.exit(1)
    else:
        logger.info("\n✅ Setup completed successfully!")
