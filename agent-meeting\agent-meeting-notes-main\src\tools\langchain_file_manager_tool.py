"""LangChain File Manager Tool for autonomous meeting intelligence."""

import json
import logging
import os
import shutil
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun

from src.helpers.file_utils import FileManager
# Import tool configs and categories
from src.constants.app import AVAILABLE_TOOLS, TOOL_CATEGORIES

logger = logging.getLogger(__name__)


class FileManagerTool(BaseTool):
    """
    LangChain tool for file management operations.
    
    This tool allows the agent to:
    - Create and manage temporary files
    - Clean up processed files and directories
    - Organize files in structured folders
    - Handle file operations safely
    - Manage working directories and cleanup
    """
    
    name: str = "file_manager_tool"
    description: str = AVAILABLE_TOOLS["file_manager_tool"]["description"]
    category: str = AVAILABLE_TOOLS["file_manager_tool"]["category"]
    
    # Declare file_manager as a class variable to avoid Pydantic validation issues
    file_manager: Optional[Any] = None
    temp_files: Optional[List[str]] = None

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.temp_files = []
    
    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute file management operations."""
        try:
            if "create temp file" in query.lower():
                return self._create_temp_file(query)
            elif "clean up" in query.lower() or "cleanup" in query.lower():
                return self._cleanup_files(query)
            elif "create directory" in query.lower():
                return self._create_directory(query)
            elif "delete" in query.lower():
                return self._delete_file(query)
            elif "list files" in query.lower():
                return self._list_files(query)
            elif "check disk space" in query.lower():
                return self._check_disk_space(query)
            elif "move file" in query.lower():
                return self._move_file(query)
            else:
                return self._get_file_status(query)
                
        except Exception as e:
            logger.error(f"File manager tool error: {e}")
            return f"Error in file management: {str(e)}"
    
    async def _arun(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute file management operations asynchronously."""
        return self._run(query, run_manager)
    
    def _create_temp_file(self, query: str) -> str:
        """Create a temporary file."""
        try:
            # Extract parameters
            content = ""
            filename = f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            # Extract content
            if "content:" in query:
                content_part = query.split("content:")[1]
                if "and name:" in content_part:
                    content = content_part.split("and name:")[0].strip().strip("'\"")
                else:
                    content = content_part.strip().strip("'\"")
            
            # Extract filename
            if "name:" in query:
                name_part = query.split("name:")[1]
                filename = name_part.strip().strip("'\"").split()[0]
            
            # Create temp directory if it doesn't exist
            temp_dir = Path("temp")
            temp_dir.mkdir(exist_ok=True)
            
            # Create temp file
            temp_file_path = temp_dir / filename
            
            with open(temp_file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Track for cleanup
            self.temp_files.append(str(temp_file_path))
            
            result = {
                "status": "success",
                "operation": "create_temp_file",
                "file_path": str(temp_file_path),
                "filename": filename,
                "content_length": len(content),
                "created_at": datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error creating temp file: {e}")
            return f"Error creating temporary file: {str(e)}"
    
    def _cleanup_files(self, query: str) -> str:
        """Clean up temporary files and directories."""
        try:
            # Extract cleanup criteria
            hours_old = 1
            if "older than" in query:
                try:
                    words = query.split("older than")[1].split()
                    for i, word in enumerate(words):
                        if word.isdigit() and i + 1 < len(words) and "hour" in words[i + 1]:
                            hours_old = int(word)
                            break
                except:
                    hours_old = 1
            
            # Cleanup temp files
            cleaned_files = []
            cleanup_errors = []
            
            # Clean tracked temp files
            for temp_file in self.temp_files[:]:
                try:
                    if os.path.exists(temp_file):
                        file_age = datetime.now() - datetime.fromtimestamp(os.path.getmtime(temp_file))
                        if file_age.total_seconds() > hours_old * 3600:
                            os.remove(temp_file)
                            cleaned_files.append(temp_file)
                            self.temp_files.remove(temp_file)
                except Exception as e:
                    cleanup_errors.append(f"Error cleaning {temp_file}: {str(e)}")
            
            # Clean temp directory
            temp_dir = Path("temp")
            if temp_dir.exists():
                try:
                    for file_path in temp_dir.iterdir():
                        if file_path.is_file():
                            file_age = datetime.now() - datetime.fromtimestamp(file_path.stat().st_mtime)
                            if file_age.total_seconds() > hours_old * 3600:
                                file_path.unlink()
                                cleaned_files.append(str(file_path))
                except Exception as e:
                    cleanup_errors.append(f"Error cleaning temp directory: {str(e)}")
            
            result = {
                "status": "success",
                "operation": "cleanup_files",
                "hours_threshold": hours_old,
                "files_cleaned": len(cleaned_files),
                "cleaned_files": cleaned_files,
                "errors": cleanup_errors,
                "cleaned_at": datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error cleaning up files: {e}")
            return f"Error cleaning up files: {str(e)}"
    
    def _create_directory(self, query: str) -> str:
        """Create directory structure."""
        try:
            # Extract directory path
            dir_path = ""
            if "structure:" in query:
                dir_path = query.split("structure:")[1].strip()
            elif "directory:" in query:
                dir_path = query.split("directory:")[1].strip()
            elif "path:" in query:
                dir_path = query.split("path:")[1].strip()
            
            if not dir_path:
                return "Please provide a directory path to create."
            
            # Create directory structure
            path_obj = Path(dir_path)
            path_obj.mkdir(parents=True, exist_ok=True)
            
            result = {
                "status": "success",
                "operation": "create_directory",
                "directory_path": str(path_obj),
                "created": path_obj.exists(),
                "created_at": datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error creating directory: {e}")
            return f"Error creating directory: {str(e)}"
    
    def _delete_file(self, query: str) -> str:
        """Delete a file or directory."""
        try:
            # Extract file path
            file_path = ""
            if "file:" in query:
                file_path = query.split("file:")[1].strip()
            elif "path:" in query:
                file_path = query.split("path:")[1].strip()
            elif "delete" in query:
                # Try to extract path after "delete"
                parts = query.split("delete")[1].strip()
                file_path = parts.split()[0] if parts else ""
            
            if not file_path:
                return "Please provide a file path to delete."
            
            path_obj = Path(file_path)
            
            if not path_obj.exists():
                return f"File or directory does not exist: {file_path}"
            
            # Delete file or directory
            if path_obj.is_file():
                path_obj.unlink()
                operation = "delete_file"
            elif path_obj.is_dir():
                shutil.rmtree(path_obj)
                operation = "delete_directory"
            else:
                return f"Unknown file type: {file_path}"
            
            # Remove from tracked temp files if applicable
            if str(path_obj) in self.temp_files:
                self.temp_files.remove(str(path_obj))
            
            result = {
                "status": "success",
                "operation": operation,
                "deleted_path": file_path,
                "deleted_at": datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error deleting file: {e}")
            return f"Error deleting file: {str(e)}"
    
    def _list_files(self, query: str) -> str:
        """List files in a directory."""
        try:
            # Extract directory path
            dir_path = "."
            if "directory:" in query:
                dir_path = query.split("directory:")[1].strip()
            elif "in:" in query:
                dir_path = query.split("in:")[1].strip()
            elif "path:" in query:
                dir_path = query.split("path:")[1].strip()
            
            path_obj = Path(dir_path)
            
            if not path_obj.exists():
                return f"Directory does not exist: {dir_path}"
            
            if not path_obj.is_dir():
                return f"Path is not a directory: {dir_path}"
            
            # List files and directories
            files = []
            directories = []
            
            for item in path_obj.iterdir():
                if item.is_file():
                    files.append({
                        "name": item.name,
                        "size": item.stat().st_size,
                        "modified": datetime.fromtimestamp(item.stat().st_mtime).isoformat()
                    })
                elif item.is_dir():
                    directories.append({
                        "name": item.name,
                        "modified": datetime.fromtimestamp(item.stat().st_mtime).isoformat()
                    })
            
            result = {
                "status": "success",
                "operation": "list_files",
                "directory_path": str(path_obj),
                "file_count": len(files),
                "directory_count": len(directories),
                "files": files,
                "directories": directories,
                "listed_at": datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error listing files: {e}")
            return f"Error listing files: {str(e)}"
    
    def _check_disk_space(self, query: str) -> str:
        """Check disk space and file system status."""
        try:
            # Get disk usage for current directory
            current_path = Path(".")
            disk_usage = shutil.disk_usage(current_path)
            
            # Calculate percentages
            total_gb = disk_usage.total / (1024**3)
            used_gb = disk_usage.used / (1024**3)
            free_gb = disk_usage.free / (1024**3)
            used_percent = (disk_usage.used / disk_usage.total) * 100
            
            # Count temp files
            temp_file_count = len(self.temp_files)
            temp_dir = Path("temp")
            temp_dir_size = 0
            if temp_dir.exists():
                temp_dir_size = sum(f.stat().st_size for f in temp_dir.rglob('*') if f.is_file())
            
            result = {
                "status": "success",
                "operation": "check_disk_space",
                "disk_usage": {
                    "total_gb": round(total_gb, 2),
                    "used_gb": round(used_gb, 2),
                    "free_gb": round(free_gb, 2),
                    "used_percent": round(used_percent, 1)
                },
                "temp_files": {
                    "tracked_count": temp_file_count,
                    "temp_dir_size_mb": round(temp_dir_size / (1024**2), 2)
                },
                "recommendations": self._get_cleanup_recommendations(used_percent, temp_file_count),
                "checked_at": datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error checking disk space: {e}")
            return f"Error checking disk space: {str(e)}"
    
    def _move_file(self, query: str) -> str:
        """Move a file to a new location."""
        try:
            # Extract source and destination
            source = ""
            destination = ""
            
            if "from:" in query and "to:" in query:
                parts = query.split("from:")[1]
                source = parts.split("to:")[0].strip()
                destination = parts.split("to:")[1].strip()
            
            if not source or not destination:
                return "Please provide source and destination paths. Format: 'move file from: <source> to: <destination>'"
            
            source_path = Path(source)
            dest_path = Path(destination)
            
            if not source_path.exists():
                return f"Source file does not exist: {source}"
            
            # Create destination directory if needed
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Move file
            shutil.move(str(source_path), str(dest_path))
            
            result = {
                "status": "success",
                "operation": "move_file",
                "source": source,
                "destination": str(dest_path),
                "moved_at": datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error moving file: {e}")
            return f"Error moving file: {str(e)}"
    
    def _get_file_status(self, query: str) -> str:
        """Get general file system status."""
        try:
            result = {
                "status": "success",
                "operation": "file_status",
                "tracked_temp_files": len(self.temp_files),
                "working_directory": str(Path.cwd()),
                "temp_directory_exists": Path("temp").exists(),
                "timestamp": datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error getting file status: {e}")
            return f"Error getting file status: {str(e)}"
    
    def _get_cleanup_recommendations(self, used_percent: float, temp_file_count: int) -> List[str]:
        """Get cleanup recommendations based on disk usage."""
        recommendations = []
        
        if used_percent > 90:
            recommendations.append("Critical: Disk usage over 90%. Immediate cleanup required.")
        elif used_percent > 80:
            recommendations.append("Warning: Disk usage over 80%. Consider cleanup.")
        
        if temp_file_count > 50:
            recommendations.append(f"High temp file count ({temp_file_count}). Consider cleanup.")
        
        if not recommendations:
            recommendations.append("Disk usage is healthy. No immediate cleanup needed.")
        
        return recommendations
