#!/usr/bin/env python3
"""
Comprehensive test for the authenticated Meeting Intelligence Agent.
This script verifies that all authentication and functionality is working correctly.
"""

import sys
import os
import asyncio
import json
from pathlib import Path
from datetime import datetime

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_authentication_status():
    """Test the current authentication status."""
    print("🔐 Testing Authentication Status...")
    
    try:
        from src.services.utility.google_auth import GoogleAuthenticator
        
        # Initialize authenticator
        auth = GoogleAuthenticator()
        
        if not auth.credentials:
            print("❌ No credentials found")
            return False
        
        if not auth.credentials.valid:
            print("❌ Credentials are invalid")
            return False
        
        print("✅ Authentication is valid")
        print(f"   Token expires: {auth.credentials.expiry}")
        print(f"   Scopes: {', '.join(auth.credentials.scopes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return False

def test_google_services():
    """Test Google service connections."""
    print("\n🔧 Testing Google Services...")
    
    try:
        from src.services.utility.google_auth import GoogleAuthenticator
        from googleapiclient.discovery import build
        
        auth = GoogleAuthenticator()
        
        if not auth.credentials or not auth.credentials.valid:
            print("❌ Invalid credentials")
            return False
        
        services_tested = []
        
        # Test Calendar API
        try:
            calendar_service = build('calendar', 'v3', credentials=auth.credentials)
            calendars = calendar_service.calendarList().list().execute()
            print(f"✅ Calendar API: {len(calendars.get('items', []))} calendars accessible")
            services_tested.append('Calendar')
        except Exception as e:
            print(f"❌ Calendar API failed: {e}")
        
        # Test Gmail API
        try:
            gmail_service = build('gmail', 'v1', credentials=auth.credentials)
            profile = gmail_service.users().getProfile(userId='me').execute()
            print(f"✅ Gmail API: Connected as {profile.get('emailAddress')}")
            services_tested.append('Gmail')
        except Exception as e:
            print(f"❌ Gmail API failed: {e}")
        
        # Test Drive API
        try:
            drive_service = build('drive', 'v3', credentials=auth.credentials)
            about = drive_service.about().get(fields='user').execute()
            print(f"✅ Drive API: Connected as {about['user']['displayName']}")
            services_tested.append('Drive')
        except Exception as e:
            print(f"❌ Drive API failed: {e}")
        
        return len(services_tested) >= 2  # At least 2 services should work
        
    except Exception as e:
        print(f"❌ Service test failed: {e}")
        return False

def test_agent_tools():
    """Test that all agent tools are working."""
    print("\n🛠️  Testing Agent Tools...")
    
    try:
        from src.agents.langchain_meeting_agent import LangChainMeetingAgent
        
        # Initialize agent
        agent = LangChainMeetingAgent()
        
        if not hasattr(agent, 'tools') or not agent.tools:
            print("❌ No tools loaded")
            return False
        
        print(f"✅ {len(agent.tools)} tools loaded:")
        
        tool_status = {}
        for tool in agent.tools:
            print(f"   - {tool.name}: {tool.description[:50]}...")
            tool_status[tool.name] = True
        
        # Check for required tools
        required_tools = ['calendar_tool', 'drive_tool', 'summarizer_tool', 'notification_tool']
        missing_tools = [tool for tool in required_tools if tool not in tool_status]
        
        if missing_tools:
            print(f"⚠️  Missing required tools: {missing_tools}")
        else:
            print("✅ All required tools are present")
        
        return len(missing_tools) == 0
        
    except Exception as e:
        print(f"❌ Tool test failed: {e}")
        return False

async def test_workflow_execution():
    """Test the main workflow execution."""
    print("\n⚙️  Testing Workflow Execution...")
    
    try:
        from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow
        
        print("🚀 Starting autonomous meeting workflow...")
        result = await run_autonomous_meeting_workflow()
        
        print(f"✅ Workflow completed with status: {result.get('status')}")
        
        # Check workflow steps
        steps = result.get('workflow_steps', [])
        print(f"📋 Workflow steps executed: {len(steps)}")
        
        for step in steps:
            status = "✅" if step.get('completed') else "❌"
            print(f"   {status} Step {step.get('step_number')}: {step.get('step_name')}")
        
        # Check execution time
        exec_time = result.get('execution_time', {})
        duration = exec_time.get('duration_seconds', 0)
        print(f"⏱️  Execution time: {duration:.2f} seconds")
        
        # Check for errors
        errors = result.get('errors', [])
        if errors:
            print(f"⚠️  Errors encountered: {len(errors)}")
            for error in errors[:3]:  # Show first 3 errors
                print(f"   - {error}")
        else:
            print("✅ No errors encountered")
        
        return result.get('status') == 'completed'
        
    except Exception as e:
        print(f"❌ Workflow test failed: {e}")
        return False

def test_api_integration():
    """Test API integration with authentication."""
    print("\n🌐 Testing API Integration...")
    
    try:
        import requests
        
        base_url = "http://localhost:8000"
        
        # Test health endpoint
        try:
            response = requests.get(f"{base_url}/health", timeout=10)
            print(f"✅ Health endpoint: {response.status_code}")
        except Exception as e:
            print(f"❌ Health endpoint failed: {e}")
            return False
        
        # Test workflow trigger
        try:
            response = requests.post(f"{base_url}/agent/trigger-workflow", 
                                   json={}, timeout=60)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Workflow trigger: {result.get('status', 'unknown')}")
            else:
                print(f"⚠️  Workflow trigger returned: {response.status_code}")
        except Exception as e:
            print(f"❌ Workflow trigger failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def generate_test_report(results):
    """Generate a comprehensive test report."""
    print("\n📊 AUTHENTICATION & FUNCTIONALITY TEST REPORT")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall Score: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("\n🎉 PERFECT! ALL AUTHENTICATION AND FUNCTIONALITY TESTS PASSED!")
        print("\n✨ Your Meeting Intelligence Agent is fully authenticated and operational!")
        print("\n🚀 Ready for production use with:")
        print("   • Google Calendar integration")
        print("   • Gmail email notifications") 
        print("   • Google Drive file storage")
        print("   • AI-powered meeting summarization")
        print("   • Complete 6-step post-meeting workflow")
        
        return True
    elif passed_tests >= total_tests * 0.8:
        print("\n✅ EXCELLENT! Most functionality is working correctly.")
        print("   Minor issues may exist but core features are operational.")
        return True
    elif passed_tests >= total_tests * 0.6:
        print("\n⚠️  GOOD. Basic functionality is working.")
        print("   Some features may need attention.")
        return False
    else:
        print("\n❌ NEEDS ATTENTION. Multiple issues detected.")
        print("   Please review the failed tests above.")
        return False

async def main():
    """Run all authentication and functionality tests."""
    print("🚀 Meeting Intelligence Agent - Authentication & Functionality Test")
    print("=" * 70)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all tests
    test_results = {}
    
    # Authentication tests
    test_results["Authentication Status"] = test_authentication_status()
    test_results["Google Services"] = test_google_services()
    
    # Functionality tests
    test_results["Agent Tools"] = test_agent_tools()
    test_results["Workflow Execution"] = await test_workflow_execution()
    test_results["API Integration"] = test_api_integration()
    
    # Generate report
    success = generate_test_report(test_results)
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        success = loop.run_until_complete(main())
        loop.close()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Test crashed: {e}")
        sys.exit(1)
